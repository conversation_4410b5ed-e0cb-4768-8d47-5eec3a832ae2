# Install script for directory: C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "C:/Program Files (x86)/git")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Release")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/MinSizeRel/git.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/RelWithDebInfo/git.exe")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    message("-- Installing app dependencies for git...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/bin/git.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    message("-- Installing app dependencies for git...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/bin/git.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    message("-- Installing app dependencies for git...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/bin/git.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    message("-- Installing app dependencies for git...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/bin/git.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-daemon.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-daemon.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/MinSizeRel/git-daemon.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/RelWithDebInfo/git-daemon.exe")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    message("-- Installing app dependencies for git-daemon...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-daemon.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    message("-- Installing app dependencies for git-daemon...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-daemon.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    message("-- Installing app dependencies for git-daemon...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-daemon.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    message("-- Installing app dependencies for git-daemon...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-daemon.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-http-backend.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-http-backend.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/MinSizeRel/git-http-backend.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/RelWithDebInfo/git-http-backend.exe")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    message("-- Installing app dependencies for git-http-backend...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-http-backend.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    message("-- Installing app dependencies for git-http-backend...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-http-backend.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    message("-- Installing app dependencies for git-http-backend...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-http-backend.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    message("-- Installing app dependencies for git-http-backend...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-http-backend.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-sh-i18n--envsubst.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-sh-i18n--envsubst.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/MinSizeRel/git-sh-i18n--envsubst.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/RelWithDebInfo/git-sh-i18n--envsubst.exe")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    message("-- Installing app dependencies for git-sh-i18n--envsubst...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-sh-i18n--envsubst.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    message("-- Installing app dependencies for git-sh-i18n--envsubst...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-sh-i18n--envsubst.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    message("-- Installing app dependencies for git-sh-i18n--envsubst...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-sh-i18n--envsubst.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    message("-- Installing app dependencies for git-sh-i18n--envsubst...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-sh-i18n--envsubst.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-shell.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-shell.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/MinSizeRel/git-shell.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/RelWithDebInfo/git-shell.exe")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    message("-- Installing app dependencies for git-shell...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/bin/git-shell.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    message("-- Installing app dependencies for git-shell...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/bin/git-shell.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    message("-- Installing app dependencies for git-shell...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/bin/git-shell.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    message("-- Installing app dependencies for git-shell...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/bin/git-shell.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/scalar.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/scalar.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/MinSizeRel/scalar.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/RelWithDebInfo/scalar.exe")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    message("-- Installing app dependencies for scalar...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/bin/scalar.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    message("-- Installing app dependencies for scalar...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/bin/scalar.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    message("-- Installing app dependencies for scalar...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/bin/scalar.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    message("-- Installing app dependencies for scalar...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/bin/scalar.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-http-fetch.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-http-fetch.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/MinSizeRel/git-http-fetch.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/RelWithDebInfo/git-http-fetch.exe")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    message("-- Installing app dependencies for git-http-fetch...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-http-fetch.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    message("-- Installing app dependencies for git-http-fetch...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-http-fetch.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    message("-- Installing app dependencies for git-http-fetch...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-http-fetch.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    message("-- Installing app dependencies for git-http-fetch...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-http-fetch.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-http-push.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-http-push.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/MinSizeRel/git-http-push.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/RelWithDebInfo/git-http-push.exe")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    message("-- Installing app dependencies for git-http-push...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-http-push.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    message("-- Installing app dependencies for git-http-push...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-http-push.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    message("-- Installing app dependencies for git-http-push...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-http-push.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    message("-- Installing app dependencies for git-http-push...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-http-push.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-imap-send.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-imap-send.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/MinSizeRel/git-imap-send.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/RelWithDebInfo/git-imap-send.exe")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    message("-- Installing app dependencies for git-imap-send...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-imap-send.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    message("-- Installing app dependencies for git-imap-send...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-imap-send.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    message("-- Installing app dependencies for git-imap-send...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-imap-send.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    message("-- Installing app dependencies for git-imap-send...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-imap-send.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-remote-http.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-remote-http.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/MinSizeRel/git-remote-http.exe")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE EXECUTABLE FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/RelWithDebInfo/git-remote-http.exe")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    message("-- Installing app dependencies for git-remote-http...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-remote-http.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    message("-- Installing app dependencies for git-remote-http...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-remote-http.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    message("-- Installing app dependencies for git-remote-http...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-remote-http.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    message("-- Installing app dependencies for git-remote-http...")
                    execute_process(COMMAND "C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe" -noprofile -executionpolicy Bypass -file "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1"
                        -targetBinary "${CMAKE_INSTALL_PREFIX}/libexec/git-core/git-remote-http.exe"
                        -installedDir "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin"
                        -OutVariable out)
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE PROGRAM FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-cvsserver")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/bin/git-receive-pack.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/bin/git-upload-archive.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/bin/git-upload-pack.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core/git.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git-shell.exe C:/Program Files (x86)/git/libexec/git-core/git-shell.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-add.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-am.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-annotate.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-apply.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-archive.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-backfill.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-bisect.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-blame.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-branch.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-bugreport.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-bundle.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-cat-file.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-check-attr.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-check-ignore.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-check-mailmap.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-check-ref-format.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-checkout--worker.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-checkout-index.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-checkout.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-clean.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-clone.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-column.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-commit-graph.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-commit-tree.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-commit.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-config.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-count-objects.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-credential-cache--daemon.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-credential-cache.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-credential-store.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-credential.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-describe.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-diagnose.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-diff-files.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-diff-index.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-diff-pairs.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-diff-tree.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-diff.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-difftool.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-fast-export.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-fast-import.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-fetch-pack.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-fetch.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-fmt-merge-msg.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-for-each-ref.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-for-each-repo.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-fsck.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-fsmonitor--daemon.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-gc.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-get-tar-commit-id.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-grep.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-hash-object.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-help.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-hook.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-index-pack.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-init-db.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-interpret-trailers.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-log.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-ls-files.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-ls-remote.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-ls-tree.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-mailinfo.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-mailsplit.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-merge-base.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-merge-file.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-merge-index.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-merge-ours.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-merge-recursive.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-merge-tree.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-merge.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-mktag.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-mktree.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-multi-pack-index.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-mv.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-name-rev.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-notes.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-pack-objects.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-pack-redundant.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-pack-refs.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-patch-id.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-prune-packed.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-prune.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-pull.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-push.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-range-diff.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-read-tree.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-rebase.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-receive-pack.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-reflog.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-refs.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-remote-ext.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-remote-fd.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-remote.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-repack.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-replace.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-replay.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-rerere.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-reset.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-rev-list.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-rev-parse.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-revert.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-rm.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-send-pack.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-shortlog.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-show-branch.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-show-index.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-show-ref.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-sparse-checkout.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-stash.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-stripspace.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-submodule--helper.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-symbolic-ref.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-tag.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-unpack-file.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-unpack-objects.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-update-index.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-update-ref.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-update-server-info.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-upload-archive.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-upload-pack.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-var.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-verify-commit.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-verify-pack.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-verify-tag.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-worktree.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-write-tree.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-cherry.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-cherry-pick.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-format-patch.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-fsck-objects.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-init.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-maintenance.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-merge-subtree.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-restore.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-show.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-stage.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-status.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-switch.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-version.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK C:/Program Files (x86)/git/bin/git.exe C:/Program Files (x86)/git/libexec/git-core//git-whatchanged.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK  C:/Program Files (x86)/git/libexec/git-core/git-remote-http.exe C:/Program Files (x86)/git/libexec/git-core//git-remote-https.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK  C:/Program Files (x86)/git/libexec/git-core/git-remote-http.exe C:/Program Files (x86)/git/libexec/git-core//git-remote-ftp.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(CREATE_LINK  C:/Program Files (x86)/git/libexec/git-core/git-remote-http.exe C:/Program Files (x86)/git/libexec/git-core//git-remote-ftps.exe)
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE PROGRAM FILES
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-difftool--helper"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-filter-branch"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-merge-octopus"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-merge-one-file"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-merge-resolve"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-mergetool"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-quiltimport"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-request-pull"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-submodule"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-web--browse"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-mergetool--lib"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-sh-i18n"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-sh-setup"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-instaweb"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-archimport.perl"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-cvsexportcommit.perl"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-cvsimport.perl"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-cvsserver.perl"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-send-email.perl"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-svn.perl"
    "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/git-p4"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/libexec/git-core" TYPE DIRECTORY FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems/../../mergetools")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/share/perl5" TYPE DIRECTORY FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/perl/build/lib/" FILES_MATCHING REGEX "/[^/]*\\.pm$")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/share/git-core/templates" TYPE DIRECTORY FILES "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/templates/blt/")
endif()

if(CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_COMPONENT MATCHES "^[a-zA-Z0-9_.+-]+$")
    set(CMAKE_INSTALL_MANIFEST "install_manifest_${CMAKE_INSTALL_COMPONENT}.txt")
  else()
    string(MD5 CMAKE_INST_COMP_HASH "${CMAKE_INSTALL_COMPONENT}")
    set(CMAKE_INSTALL_MANIFEST "install_manifest_${CMAKE_INST_COMP_HASH}.txt")
    unset(CMAKE_INST_COMP_HASH)
  endif()
else()
  set(CMAKE_INSTALL_MANIFEST "install_manifest.txt")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  string(REPLACE ";" "\n" CMAKE_INSTALL_MANIFEST_CONTENT
       "${CMAKE_INSTALL_MANIFEST_FILES}")
  file(WRITE "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/${CMAKE_INSTALL_MANIFEST}"
     "${CMAKE_INSTALL_MANIFEST_CONTENT}")
endif()
