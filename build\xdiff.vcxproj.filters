﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\xdiff\xdiffi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\xdiff\xemit.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\xdiff\xhistogram.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\xdiff\xmerge.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\xdiff\xpatience.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\xdiff\xprepare.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\xdiff\xutils.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{C180A924-5CB5-3610-8243-A801B3263C3A}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
