﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{2576421A-2D4D-3A2B-9276-98F133EB1DB2}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>git-links</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\b0f885988ecc73d15da5f717fc823e7a\git-add.exe.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generating git-add.exe, git-am.exe, git-annotate.exe, git-apply.exe, git-archive.exe, git-backfill.exe, git-bisect.exe, git-blame.exe, git-branch.exe, git-bugreport.exe, git-bundle.exe, git-cat-file.exe, git-check-attr.exe, git-check-ignore.exe, git-check-mailmap.exe, git-check-ref-format.exe, git-checkout--worker.exe, git-checkout-index.exe, git-checkout.exe, git-clean.exe, git-clone.exe, git-column.exe, git-commit-graph.exe, git-commit-tree.exe, git-commit.exe, git-config.exe, git-count-objects.exe, git-credential-cache--daemon.exe, git-credential-cache.exe, git-credential-store.exe, git-credential.exe, git-describe.exe, git-diagnose.exe, git-diff-files.exe, git-diff-index.exe, git-diff-pairs.exe, git-diff-tree.exe, git-diff.exe, git-difftool.exe, git-fast-export.exe, git-fast-import.exe, git-fetch-pack.exe, git-fetch.exe, git-fmt-merge-msg.exe, git-for-each-ref.exe, git-for-each-repo.exe, git-fsck.exe, git-fsmonitor--daemon.exe, git-gc.exe, git-get-tar-commit-id.exe, git-grep.exe, git-hash-object.exe, git-help.exe, git-hook.exe, git-index-pack.exe, git-init-db.exe, git-interpret-trailers.exe, git-log.exe, git-ls-files.exe, git-ls-remote.exe, git-ls-tree.exe, git-mailinfo.exe, git-mailsplit.exe, git-merge-base.exe, git-merge-file.exe, git-merge-index.exe, git-merge-ours.exe, git-merge-recursive.exe, git-merge-tree.exe, git-merge.exe, git-mktag.exe, git-mktree.exe, git-multi-pack-index.exe, git-mv.exe, git-name-rev.exe, git-notes.exe, git-pack-objects.exe, git-pack-redundant.exe, git-pack-refs.exe, git-patch-id.exe, git-prune-packed.exe, git-prune.exe, git-pull.exe, git-push.exe, git-range-diff.exe, git-read-tree.exe, git-rebase.exe, git-receive-pack.exe, git-reflog.exe, git-refs.exe, git-remote-ext.exe, git-remote-fd.exe, git-remote.exe, git-repack.exe, git-replace.exe, git-replay.exe, git-rerere.exe, git-reset.exe, git-rev-list.exe, git-rev-parse.exe, git-revert.exe, git-rm.exe, git-send-pack.exe, git-shortlog.exe, git-show-branch.exe, git-show-index.exe, git-show-ref.exe, git-sparse-checkout.exe, git-stash.exe, git-stripspace.exe, git-submodule--helper.exe, git-symbolic-ref.exe, git-tag.exe, git-unpack-file.exe, git-unpack-objects.exe, git-update-index.exe, git-update-ref.exe, git-update-server-info.exe, git-upload-archive.exe, git-upload-pack.exe, git-var.exe, git-verify-commit.exe, git-verify-pack.exe, git-verify-tag.exe, git-worktree.exe, git-write-tree.exe, git-cherry.exe, git-cherry-pick.exe, git-format-patch.exe, git-fsck-objects.exe, git-init.exe, git-maintenance.exe, git-merge-subtree.exe, git-restore.exe, git-show.exe, git-stage.exe, git-status.exe, git-switch.exe, git-version.exe, git-whatchanged.exe, git-remote-https.exe, git-remote-ftp.exe, git-remote-ftps.exe</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -P C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CreateLinks.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-http.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-add.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-am.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-annotate.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-apply.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-backfill.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bisect.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-blame.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bugreport.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bundle.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cat-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-attr.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ignore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-mailmap.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ref-format.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout--worker.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clean.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clone.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-column.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-graph.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-config.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-count-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-store.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-describe.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diagnose.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-pairs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-difftool.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-export.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-import.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fmt-merge-msg.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-repo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsmonitor--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-gc.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-get-tar-commit-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-grep.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hash-object.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-help.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hook.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-index-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init-db.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-interpret-trailers.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-log.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailinfo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailsplit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-base.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-ours.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-recursive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-multi-pack-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mv.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-name-rev.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-notes.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-redundant.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-patch-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune-packed.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pull.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-push.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-range-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-read-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rebase.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-receive-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reflog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ext.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-fd.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-repack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replay.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rerere.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reset.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-list.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-parse.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-revert.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rm.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-send-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-shortlog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-sparse-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stash.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stripspace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-submodule--helper.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-symbolic-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-server-info.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-var.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-worktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-write-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry-pick.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-format-patch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-maintenance.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-subtree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-restore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stage.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-status.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-switch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-version.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-whatchanged.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-https.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftp.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftps.exe</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generating git-add.exe, git-am.exe, git-annotate.exe, git-apply.exe, git-archive.exe, git-backfill.exe, git-bisect.exe, git-blame.exe, git-branch.exe, git-bugreport.exe, git-bundle.exe, git-cat-file.exe, git-check-attr.exe, git-check-ignore.exe, git-check-mailmap.exe, git-check-ref-format.exe, git-checkout--worker.exe, git-checkout-index.exe, git-checkout.exe, git-clean.exe, git-clone.exe, git-column.exe, git-commit-graph.exe, git-commit-tree.exe, git-commit.exe, git-config.exe, git-count-objects.exe, git-credential-cache--daemon.exe, git-credential-cache.exe, git-credential-store.exe, git-credential.exe, git-describe.exe, git-diagnose.exe, git-diff-files.exe, git-diff-index.exe, git-diff-pairs.exe, git-diff-tree.exe, git-diff.exe, git-difftool.exe, git-fast-export.exe, git-fast-import.exe, git-fetch-pack.exe, git-fetch.exe, git-fmt-merge-msg.exe, git-for-each-ref.exe, git-for-each-repo.exe, git-fsck.exe, git-fsmonitor--daemon.exe, git-gc.exe, git-get-tar-commit-id.exe, git-grep.exe, git-hash-object.exe, git-help.exe, git-hook.exe, git-index-pack.exe, git-init-db.exe, git-interpret-trailers.exe, git-log.exe, git-ls-files.exe, git-ls-remote.exe, git-ls-tree.exe, git-mailinfo.exe, git-mailsplit.exe, git-merge-base.exe, git-merge-file.exe, git-merge-index.exe, git-merge-ours.exe, git-merge-recursive.exe, git-merge-tree.exe, git-merge.exe, git-mktag.exe, git-mktree.exe, git-multi-pack-index.exe, git-mv.exe, git-name-rev.exe, git-notes.exe, git-pack-objects.exe, git-pack-redundant.exe, git-pack-refs.exe, git-patch-id.exe, git-prune-packed.exe, git-prune.exe, git-pull.exe, git-push.exe, git-range-diff.exe, git-read-tree.exe, git-rebase.exe, git-receive-pack.exe, git-reflog.exe, git-refs.exe, git-remote-ext.exe, git-remote-fd.exe, git-remote.exe, git-repack.exe, git-replace.exe, git-replay.exe, git-rerere.exe, git-reset.exe, git-rev-list.exe, git-rev-parse.exe, git-revert.exe, git-rm.exe, git-send-pack.exe, git-shortlog.exe, git-show-branch.exe, git-show-index.exe, git-show-ref.exe, git-sparse-checkout.exe, git-stash.exe, git-stripspace.exe, git-submodule--helper.exe, git-symbolic-ref.exe, git-tag.exe, git-unpack-file.exe, git-unpack-objects.exe, git-update-index.exe, git-update-ref.exe, git-update-server-info.exe, git-upload-archive.exe, git-upload-pack.exe, git-var.exe, git-verify-commit.exe, git-verify-pack.exe, git-verify-tag.exe, git-worktree.exe, git-write-tree.exe, git-cherry.exe, git-cherry-pick.exe, git-format-patch.exe, git-fsck-objects.exe, git-init.exe, git-maintenance.exe, git-merge-subtree.exe, git-restore.exe, git-show.exe, git-stage.exe, git-status.exe, git-switch.exe, git-version.exe, git-whatchanged.exe, git-remote-https.exe, git-remote-ftp.exe, git-remote-ftps.exe</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -P C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CreateLinks.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-http.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-add.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-am.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-annotate.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-apply.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-backfill.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bisect.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-blame.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bugreport.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bundle.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cat-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-attr.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ignore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-mailmap.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ref-format.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout--worker.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clean.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clone.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-column.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-graph.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-config.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-count-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-store.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-describe.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diagnose.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-pairs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-difftool.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-export.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-import.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fmt-merge-msg.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-repo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsmonitor--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-gc.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-get-tar-commit-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-grep.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hash-object.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-help.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hook.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-index-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init-db.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-interpret-trailers.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-log.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailinfo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailsplit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-base.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-ours.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-recursive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-multi-pack-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mv.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-name-rev.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-notes.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-redundant.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-patch-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune-packed.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pull.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-push.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-range-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-read-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rebase.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-receive-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reflog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ext.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-fd.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-repack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replay.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rerere.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reset.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-list.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-parse.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-revert.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rm.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-send-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-shortlog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-sparse-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stash.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stripspace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-submodule--helper.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-symbolic-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-server-info.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-var.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-worktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-write-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry-pick.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-format-patch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-maintenance.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-subtree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-restore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stage.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-status.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-switch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-version.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-whatchanged.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-https.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftp.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftps.exe</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Generating git-add.exe, git-am.exe, git-annotate.exe, git-apply.exe, git-archive.exe, git-backfill.exe, git-bisect.exe, git-blame.exe, git-branch.exe, git-bugreport.exe, git-bundle.exe, git-cat-file.exe, git-check-attr.exe, git-check-ignore.exe, git-check-mailmap.exe, git-check-ref-format.exe, git-checkout--worker.exe, git-checkout-index.exe, git-checkout.exe, git-clean.exe, git-clone.exe, git-column.exe, git-commit-graph.exe, git-commit-tree.exe, git-commit.exe, git-config.exe, git-count-objects.exe, git-credential-cache--daemon.exe, git-credential-cache.exe, git-credential-store.exe, git-credential.exe, git-describe.exe, git-diagnose.exe, git-diff-files.exe, git-diff-index.exe, git-diff-pairs.exe, git-diff-tree.exe, git-diff.exe, git-difftool.exe, git-fast-export.exe, git-fast-import.exe, git-fetch-pack.exe, git-fetch.exe, git-fmt-merge-msg.exe, git-for-each-ref.exe, git-for-each-repo.exe, git-fsck.exe, git-fsmonitor--daemon.exe, git-gc.exe, git-get-tar-commit-id.exe, git-grep.exe, git-hash-object.exe, git-help.exe, git-hook.exe, git-index-pack.exe, git-init-db.exe, git-interpret-trailers.exe, git-log.exe, git-ls-files.exe, git-ls-remote.exe, git-ls-tree.exe, git-mailinfo.exe, git-mailsplit.exe, git-merge-base.exe, git-merge-file.exe, git-merge-index.exe, git-merge-ours.exe, git-merge-recursive.exe, git-merge-tree.exe, git-merge.exe, git-mktag.exe, git-mktree.exe, git-multi-pack-index.exe, git-mv.exe, git-name-rev.exe, git-notes.exe, git-pack-objects.exe, git-pack-redundant.exe, git-pack-refs.exe, git-patch-id.exe, git-prune-packed.exe, git-prune.exe, git-pull.exe, git-push.exe, git-range-diff.exe, git-read-tree.exe, git-rebase.exe, git-receive-pack.exe, git-reflog.exe, git-refs.exe, git-remote-ext.exe, git-remote-fd.exe, git-remote.exe, git-repack.exe, git-replace.exe, git-replay.exe, git-rerere.exe, git-reset.exe, git-rev-list.exe, git-rev-parse.exe, git-revert.exe, git-rm.exe, git-send-pack.exe, git-shortlog.exe, git-show-branch.exe, git-show-index.exe, git-show-ref.exe, git-sparse-checkout.exe, git-stash.exe, git-stripspace.exe, git-submodule--helper.exe, git-symbolic-ref.exe, git-tag.exe, git-unpack-file.exe, git-unpack-objects.exe, git-update-index.exe, git-update-ref.exe, git-update-server-info.exe, git-upload-archive.exe, git-upload-pack.exe, git-var.exe, git-verify-commit.exe, git-verify-pack.exe, git-verify-tag.exe, git-worktree.exe, git-write-tree.exe, git-cherry.exe, git-cherry-pick.exe, git-format-patch.exe, git-fsck-objects.exe, git-init.exe, git-maintenance.exe, git-merge-subtree.exe, git-restore.exe, git-show.exe, git-stage.exe, git-status.exe, git-switch.exe, git-version.exe, git-whatchanged.exe, git-remote-https.exe, git-remote-ftp.exe, git-remote-ftps.exe</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -P C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CreateLinks.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\MinSizeRel\git.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\MinSizeRel\git-remote-http.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-add.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-am.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-annotate.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-apply.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-backfill.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bisect.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-blame.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bugreport.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bundle.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cat-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-attr.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ignore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-mailmap.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ref-format.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout--worker.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clean.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clone.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-column.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-graph.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-config.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-count-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-store.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-describe.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diagnose.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-pairs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-difftool.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-export.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-import.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fmt-merge-msg.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-repo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsmonitor--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-gc.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-get-tar-commit-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-grep.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hash-object.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-help.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hook.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-index-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init-db.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-interpret-trailers.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-log.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailinfo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailsplit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-base.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-ours.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-recursive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-multi-pack-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mv.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-name-rev.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-notes.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-redundant.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-patch-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune-packed.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pull.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-push.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-range-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-read-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rebase.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-receive-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reflog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ext.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-fd.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-repack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replay.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rerere.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reset.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-list.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-parse.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-revert.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rm.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-send-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-shortlog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-sparse-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stash.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stripspace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-submodule--helper.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-symbolic-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-server-info.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-var.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-worktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-write-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry-pick.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-format-patch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-maintenance.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-subtree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-restore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stage.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-status.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-switch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-version.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-whatchanged.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-https.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftp.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftps.exe</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Generating git-add.exe, git-am.exe, git-annotate.exe, git-apply.exe, git-archive.exe, git-backfill.exe, git-bisect.exe, git-blame.exe, git-branch.exe, git-bugreport.exe, git-bundle.exe, git-cat-file.exe, git-check-attr.exe, git-check-ignore.exe, git-check-mailmap.exe, git-check-ref-format.exe, git-checkout--worker.exe, git-checkout-index.exe, git-checkout.exe, git-clean.exe, git-clone.exe, git-column.exe, git-commit-graph.exe, git-commit-tree.exe, git-commit.exe, git-config.exe, git-count-objects.exe, git-credential-cache--daemon.exe, git-credential-cache.exe, git-credential-store.exe, git-credential.exe, git-describe.exe, git-diagnose.exe, git-diff-files.exe, git-diff-index.exe, git-diff-pairs.exe, git-diff-tree.exe, git-diff.exe, git-difftool.exe, git-fast-export.exe, git-fast-import.exe, git-fetch-pack.exe, git-fetch.exe, git-fmt-merge-msg.exe, git-for-each-ref.exe, git-for-each-repo.exe, git-fsck.exe, git-fsmonitor--daemon.exe, git-gc.exe, git-get-tar-commit-id.exe, git-grep.exe, git-hash-object.exe, git-help.exe, git-hook.exe, git-index-pack.exe, git-init-db.exe, git-interpret-trailers.exe, git-log.exe, git-ls-files.exe, git-ls-remote.exe, git-ls-tree.exe, git-mailinfo.exe, git-mailsplit.exe, git-merge-base.exe, git-merge-file.exe, git-merge-index.exe, git-merge-ours.exe, git-merge-recursive.exe, git-merge-tree.exe, git-merge.exe, git-mktag.exe, git-mktree.exe, git-multi-pack-index.exe, git-mv.exe, git-name-rev.exe, git-notes.exe, git-pack-objects.exe, git-pack-redundant.exe, git-pack-refs.exe, git-patch-id.exe, git-prune-packed.exe, git-prune.exe, git-pull.exe, git-push.exe, git-range-diff.exe, git-read-tree.exe, git-rebase.exe, git-receive-pack.exe, git-reflog.exe, git-refs.exe, git-remote-ext.exe, git-remote-fd.exe, git-remote.exe, git-repack.exe, git-replace.exe, git-replay.exe, git-rerere.exe, git-reset.exe, git-rev-list.exe, git-rev-parse.exe, git-revert.exe, git-rm.exe, git-send-pack.exe, git-shortlog.exe, git-show-branch.exe, git-show-index.exe, git-show-ref.exe, git-sparse-checkout.exe, git-stash.exe, git-stripspace.exe, git-submodule--helper.exe, git-symbolic-ref.exe, git-tag.exe, git-unpack-file.exe, git-unpack-objects.exe, git-update-index.exe, git-update-ref.exe, git-update-server-info.exe, git-upload-archive.exe, git-upload-pack.exe, git-var.exe, git-verify-commit.exe, git-verify-pack.exe, git-verify-tag.exe, git-worktree.exe, git-write-tree.exe, git-cherry.exe, git-cherry-pick.exe, git-format-patch.exe, git-fsck-objects.exe, git-init.exe, git-maintenance.exe, git-merge-subtree.exe, git-restore.exe, git-show.exe, git-stage.exe, git-status.exe, git-switch.exe, git-version.exe, git-whatchanged.exe, git-remote-https.exe, git-remote-ftp.exe, git-remote-ftps.exe</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -P C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CreateLinks.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\RelWithDebInfo\git.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\RelWithDebInfo\git-remote-http.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-add.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-am.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-annotate.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-apply.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-backfill.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bisect.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-blame.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bugreport.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bundle.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cat-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-attr.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ignore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-mailmap.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ref-format.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout--worker.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clean.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clone.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-column.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-graph.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-config.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-count-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-store.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-describe.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diagnose.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-pairs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-difftool.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-export.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-import.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fmt-merge-msg.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-repo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsmonitor--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-gc.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-get-tar-commit-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-grep.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hash-object.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-help.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hook.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-index-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init-db.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-interpret-trailers.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-log.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailinfo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailsplit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-base.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-ours.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-recursive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-multi-pack-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mv.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-name-rev.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-notes.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-redundant.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-patch-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune-packed.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pull.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-push.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-range-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-read-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rebase.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-receive-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reflog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ext.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-fd.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-repack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replay.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rerere.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reset.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-list.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-parse.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-revert.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rm.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-send-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-shortlog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-sparse-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stash.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stripspace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-submodule--helper.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-symbolic-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-server-info.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-var.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-worktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-write-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry-pick.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-format-patch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-maintenance.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-subtree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-restore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stage.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-status.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-switch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-version.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-whatchanged.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-https.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftp.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftps.exe</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\15e124c2f35056f23304497decf09d62\git-links.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-add.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-am.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-annotate.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-apply.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-backfill.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bisect.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-blame.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bugreport.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bundle.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cat-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-attr.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ignore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-mailmap.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ref-format.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout--worker.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clean.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clone.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-column.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-graph.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-config.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-count-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-store.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-describe.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diagnose.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-pairs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-difftool.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-export.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-import.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fmt-merge-msg.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-repo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsmonitor--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-gc.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-get-tar-commit-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-grep.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hash-object.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-help.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hook.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-index-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init-db.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-interpret-trailers.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-log.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailinfo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailsplit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-base.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-ours.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-recursive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-multi-pack-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mv.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-name-rev.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-notes.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-redundant.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-patch-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune-packed.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pull.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-push.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-range-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-read-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rebase.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-receive-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reflog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ext.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-fd.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-repack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replay.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rerere.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reset.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-list.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-parse.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-revert.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rm.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-send-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-shortlog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-sparse-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stash.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stripspace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-submodule--helper.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-symbolic-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-server-info.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-var.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-worktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-write-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry-pick.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-format-patch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-maintenance.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-subtree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-restore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stage.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-status.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-switch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-version.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-whatchanged.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-https.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftp.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftps.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\git-links</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-add.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-am.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-annotate.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-apply.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-backfill.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bisect.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-blame.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bugreport.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bundle.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cat-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-attr.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ignore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-mailmap.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ref-format.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout--worker.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clean.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clone.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-column.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-graph.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-config.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-count-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-store.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-describe.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diagnose.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-pairs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-difftool.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-export.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-import.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fmt-merge-msg.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-repo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsmonitor--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-gc.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-get-tar-commit-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-grep.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hash-object.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-help.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hook.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-index-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init-db.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-interpret-trailers.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-log.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailinfo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailsplit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-base.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-ours.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-recursive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-multi-pack-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mv.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-name-rev.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-notes.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-redundant.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-patch-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune-packed.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pull.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-push.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-range-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-read-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rebase.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-receive-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reflog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ext.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-fd.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-repack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replay.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rerere.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reset.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-list.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-parse.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-revert.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rm.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-send-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-shortlog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-sparse-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stash.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stripspace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-submodule--helper.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-symbolic-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-server-info.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-var.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-worktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-write-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry-pick.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-format-patch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-maintenance.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-subtree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-restore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stage.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-status.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-switch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-version.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-whatchanged.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-https.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftp.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftps.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\git-links</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-add.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-am.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-annotate.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-apply.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-backfill.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bisect.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-blame.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bugreport.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bundle.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cat-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-attr.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ignore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-mailmap.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ref-format.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout--worker.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clean.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clone.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-column.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-graph.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-config.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-count-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-store.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-describe.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diagnose.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-pairs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-difftool.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-export.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-import.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fmt-merge-msg.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-repo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsmonitor--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-gc.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-get-tar-commit-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-grep.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hash-object.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-help.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hook.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-index-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init-db.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-interpret-trailers.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-log.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailinfo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailsplit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-base.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-ours.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-recursive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-multi-pack-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mv.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-name-rev.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-notes.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-redundant.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-patch-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune-packed.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pull.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-push.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-range-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-read-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rebase.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-receive-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reflog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ext.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-fd.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-repack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replay.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rerere.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reset.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-list.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-parse.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-revert.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rm.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-send-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-shortlog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-sparse-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stash.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stripspace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-submodule--helper.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-symbolic-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-server-info.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-var.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-worktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-write-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry-pick.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-format-patch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-maintenance.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-subtree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-restore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stage.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-status.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-switch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-version.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-whatchanged.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-https.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftp.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftps.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\git-links</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-add.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-am.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-annotate.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-apply.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-backfill.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bisect.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-blame.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bugreport.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-bundle.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cat-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-attr.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ignore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-mailmap.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-check-ref-format.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout--worker.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clean.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-clone.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-column.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-graph.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-config.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-count-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-cache.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential-store.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-credential.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-describe.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diagnose.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-pairs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-difftool.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-export.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fast-import.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fetch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fmt-merge-msg.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-for-each-repo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsmonitor--daemon.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-gc.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-get-tar-commit-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-grep.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hash-object.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-help.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-hook.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-index-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init-db.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-interpret-trailers.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-log.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-files.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-ls-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailinfo.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mailsplit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-base.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-ours.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-recursive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-multi-pack-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-mv.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-name-rev.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-notes.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-redundant.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pack-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-patch-id.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune-packed.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-prune.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-pull.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-push.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-range-diff.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-read-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rebase.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-receive-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reflog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-refs.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ext.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-fd.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-repack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-replay.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rerere.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-reset.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-list.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rev-parse.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-revert.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-rm.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-send-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-shortlog.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-branch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-sparse-checkout.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stash.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stripspace.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-submodule--helper.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-symbolic-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-file.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-unpack-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-index.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-ref.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-update-server-info.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-archive.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-upload-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-var.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-commit.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-pack.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-verify-tag.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-worktree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-write-tree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-cherry-pick.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-format-patch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-fsck-objects.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-init.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-maintenance.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-merge-subtree.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-restore.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-show.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-stage.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-status.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-switch.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-version.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-whatchanged.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-https.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftp.exe;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-ftps.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\git-links</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -SC:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems -BC:/Users/<USER>/Desktop/workspace/tools/git/git/build --check-stamp-file C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTest.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestUseLaunchers.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckFunctionExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckStructHasMember.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckTypeSize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\DartConfiguration.tcl.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindEXPAT.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindIconv.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPkgConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\expat\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\iconv\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\scripts\buildsystems\vcpkg.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\description;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\applypatch-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\fsmonitor-watchman.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\post-update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-applypatch.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-merge-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-push.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-rebase.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-receive.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\prepare-commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\push-to-checkout.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\sendemail-validate.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\info\exclude;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -SC:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems -BC:/Users/<USER>/Desktop/workspace/tools/git/git/build --check-stamp-file C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTest.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestUseLaunchers.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckFunctionExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckStructHasMember.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckTypeSize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\DartConfiguration.tcl.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindEXPAT.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindIconv.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPkgConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\expat\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\iconv\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\scripts\buildsystems\vcpkg.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\description;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\applypatch-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\fsmonitor-watchman.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\post-update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-applypatch.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-merge-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-push.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-rebase.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-receive.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\prepare-commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\push-to-checkout.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\sendemail-validate.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\info\exclude;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -SC:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems -BC:/Users/<USER>/Desktop/workspace/tools/git/git/build --check-stamp-file C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTest.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestUseLaunchers.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckFunctionExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckStructHasMember.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckTypeSize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\DartConfiguration.tcl.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindEXPAT.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindIconv.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPkgConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\expat\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\iconv\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\scripts\buildsystems\vcpkg.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\description;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\applypatch-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\fsmonitor-watchman.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\post-update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-applypatch.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-merge-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-push.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-rebase.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-receive.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\prepare-commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\push-to-checkout.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\sendemail-validate.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\info\exclude;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -SC:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems -BC:/Users/<USER>/Desktop/workspace/tools/git/git/build --check-stamp-file C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTest.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestUseLaunchers.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckFunctionExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckStructHasMember.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckTypeSize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\DartConfiguration.tcl.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindEXPAT.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindIconv.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPkgConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\expat\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\iconv\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\scripts\buildsystems\vcpkg.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\description;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\applypatch-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\fsmonitor-watchman.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\post-update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-applypatch.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-merge-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-push.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-rebase.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-receive.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\prepare-commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\push-to-checkout.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\sendemail-validate.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\info\exclude;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\git-links">
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\ZERO_CHECK.vcxproj">
      <Project>{1EA13B83-14DD-3A1E-96AC-46FB2F067586}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git.vcxproj">
      <Project>{62C64C83-94CD-313C-85AC-9DB60800E8DB}</Project>
      <Name>git</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\git-remote-http.vcxproj">
      <Project>{E7A39109-413F-38BB-A90F-58A7F1455F43}</Project>
      <Name>git-remote-http</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>