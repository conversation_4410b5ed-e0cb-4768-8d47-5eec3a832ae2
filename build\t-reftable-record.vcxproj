﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{231EC9E2-0452-3194-976C-BE15D3A8DCA8}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <VcpkgEnabled>false</VcpkgEnabled>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>t-reftable-record</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="do_not_import_user.props" Condition="exists('do_not_import_user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">t-reftable-record.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">t-reftable-record</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">t-reftable-record.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">t-reftable-record</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">t-reftable-record.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">t-reftable-record</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">t-reftable-record.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">t-reftable-record</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard_C>stdc11</LanguageStandard_C>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NO_GETTEXT;GIT_HOST_CPU="AMD64";SHA256_BLK;INTERNAL_QSORT;RUNTIME_PREFIX;NO_OPENSSL;SHA1_DC;SHA1DC_NO_STANDARD_INCLUDES;SHA1DC_INIT_SAFE_HASH_DEFAULT=0;SHA1DC_CUSTOM_INCLUDE_SHA1_C="git-compat-util.h";SHA1DC_CUSTOM_INCLUDE_UBC_CHECK_C="git-compat-util.h";PAGER_ENV="LESS=FRX LV=-c";GIT_EXEC_PATH="libexec/git-core";GIT_LOCALE_PATH="share/locale";GIT_MAN_PATH="share/man";GIT_INFO_PATH="share/info";GIT_HTML_PATH="share/doc/git-doc";DEFAULT_HELP_FORMAT="html";DEFAULT_GIT_TEMPLATE_DIR="share/git-core/templates";BINDIR="bin";FALLBACK_RUNTIME_PREFIX="/mingw64";ETC_GITATTRIBUTES="../etc/gitattributes";ETC_GITCONFIG="../etc/gitconfig";_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;HAVE_ALLOCA_H;NO_POSIX_GOODIES;NATIVE_CRLF;NO_UNIX_SOCKETS;_CONSOLE;DETECT_MSYS_TTY;STRIP_EXTENSION=".exe";NO_SYMLINK_HEAD;UNRELIABLE_FSTAT;NOGDI;OBJECT_CREATION_MODE=1;__USE_MINGW_ANSI_STDIO=0;USE_NED_ALLOCATOR;OVERRIDE_STRDUP;MMAP_PREVENTS_DELETE;USE_WIN32_MMAP;HAVE_WPGMPTR;ENSURE_MSYSTEM_IS_SET;HAVE_RTLGENRANDOM;SUPPORTS_SIMPLE_IPC;HAVE_FSMONITOR_DAEMON_BACKEND;HAVE_FSMONITOR_OS_SETTINGS;NO_LIBGEN_H;NO_SYS_SELECT_H;NO_SYS_POLL_H;NO_POLL_H;NO_STRCASESTR;NO_MEMMEM;NO_STRLCPY;NO_SETENV;NO_MKDTEMP;NO_POLL;NO_PREAD;NO_ST_BLOCKS_IN_STRUCT_STAT;NO_REGEX;NO_MBSUPPORT;GAWK;ICONV_OMITS_BOM;USE_CURL_FOR_IMAP_SEND;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;NO_GETTEXT;GIT_HOST_CPU=\"AMD64\";SHA256_BLK;INTERNAL_QSORT;RUNTIME_PREFIX;NO_OPENSSL;SHA1_DC;SHA1DC_NO_STANDARD_INCLUDES;SHA1DC_INIT_SAFE_HASH_DEFAULT=0;SHA1DC_CUSTOM_INCLUDE_SHA1_C=\"git-compat-util.h\";SHA1DC_CUSTOM_INCLUDE_UBC_CHECK_C=\"git-compat-util.h\";PAGER_ENV=\"LESS=FRX LV=-c\";GIT_EXEC_PATH=\"libexec/git-core\";GIT_LOCALE_PATH=\"share/locale\";GIT_MAN_PATH=\"share/man\";GIT_INFO_PATH=\"share/info\";GIT_HTML_PATH=\"share/doc/git-doc\";DEFAULT_HELP_FORMAT=\"html\";DEFAULT_GIT_TEMPLATE_DIR=\"share/git-core/templates\";BINDIR=\"bin\";FALLBACK_RUNTIME_PREFIX=\"/mingw64\";ETC_GITATTRIBUTES=\"../etc/gitattributes\";ETC_GITCONFIG=\"../etc/gitconfig\";_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;HAVE_ALLOCA_H;NO_POSIX_GOODIES;NATIVE_CRLF;NO_UNIX_SOCKETS;_CONSOLE;DETECT_MSYS_TTY;STRIP_EXTENSION=\".exe\";NO_SYMLINK_HEAD;UNRELIABLE_FSTAT;NOGDI;OBJECT_CREATION_MODE=1;__USE_MINGW_ANSI_STDIO=0;USE_NED_ALLOCATOR;OVERRIDE_STRDUP;MMAP_PREVENTS_DELETE;USE_WIN32_MMAP;HAVE_WPGMPTR;ENSURE_MSYSTEM_IS_SET;HAVE_RTLGENRANDOM;SUPPORTS_SIMPLE_IPC;HAVE_FSMONITOR_DAEMON_BACKEND;HAVE_FSMONITOR_OS_SETTINGS;NO_LIBGEN_H;NO_SYS_SELECT_H;NO_SYS_POLL_H;NO_POLL_H;NO_STRCASESTR;NO_MEMMEM;NO_STRLCPY;NO_SETENV;NO_MKDTEMP;NO_POLL;NO_PREAD;NO_ST_BLOCKS_IN_STRUCT_STAT;NO_REGEX;NO_MBSUPPORT;GAWK;ICONV_OMITS_BOM;USE_CURL_FOR_IMAP_SEND;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary C:/Users/<USER>/Desktop/workspace/tools/git/git/build/t/unit-tests/bin/t-reftable-record.exe -installedDir C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>Debug\unit-test-lib.lib;Debug\clar-test-lib.lib;Debug\libgit.lib;Debug\xdiff.lib;Debug\reftable.lib;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\debug\lib\zlibd.lib;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\debug\lib\iconv.lib;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\debug\lib\charset.lib;ws2_32.lib;ntdll.lib;git.res;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4217 /IGNORE:4049 invalidcontinue.obj</AdditionalOptions>
      <EntryPointSymbol>wmainCRTStartup</EntryPointSymbol>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/workspace/tools/git/git/build/Debug/t-reftable-record.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/workspace/tools/git/git/build/t/unit-tests/bin/t-reftable-record.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard_C>stdc11</LanguageStandard_C>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;NO_GETTEXT;GIT_HOST_CPU="AMD64";SHA256_BLK;INTERNAL_QSORT;RUNTIME_PREFIX;NO_OPENSSL;SHA1_DC;SHA1DC_NO_STANDARD_INCLUDES;SHA1DC_INIT_SAFE_HASH_DEFAULT=0;SHA1DC_CUSTOM_INCLUDE_SHA1_C="git-compat-util.h";SHA1DC_CUSTOM_INCLUDE_UBC_CHECK_C="git-compat-util.h";PAGER_ENV="LESS=FRX LV=-c";GIT_EXEC_PATH="libexec/git-core";GIT_LOCALE_PATH="share/locale";GIT_MAN_PATH="share/man";GIT_INFO_PATH="share/info";GIT_HTML_PATH="share/doc/git-doc";DEFAULT_HELP_FORMAT="html";DEFAULT_GIT_TEMPLATE_DIR="share/git-core/templates";BINDIR="bin";FALLBACK_RUNTIME_PREFIX="/mingw64";ETC_GITATTRIBUTES="../etc/gitattributes";ETC_GITCONFIG="../etc/gitconfig";_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;HAVE_ALLOCA_H;NO_POSIX_GOODIES;NATIVE_CRLF;NO_UNIX_SOCKETS;_CONSOLE;DETECT_MSYS_TTY;STRIP_EXTENSION=".exe";NO_SYMLINK_HEAD;UNRELIABLE_FSTAT;NOGDI;OBJECT_CREATION_MODE=1;__USE_MINGW_ANSI_STDIO=0;USE_NED_ALLOCATOR;OVERRIDE_STRDUP;MMAP_PREVENTS_DELETE;USE_WIN32_MMAP;HAVE_WPGMPTR;ENSURE_MSYSTEM_IS_SET;HAVE_RTLGENRANDOM;SUPPORTS_SIMPLE_IPC;HAVE_FSMONITOR_DAEMON_BACKEND;HAVE_FSMONITOR_OS_SETTINGS;NO_LIBGEN_H;NO_SYS_SELECT_H;NO_SYS_POLL_H;NO_POLL_H;NO_STRCASESTR;NO_MEMMEM;NO_STRLCPY;NO_SETENV;NO_MKDTEMP;NO_POLL;NO_PREAD;NO_ST_BLOCKS_IN_STRUCT_STAT;NO_REGEX;NO_MBSUPPORT;GAWK;ICONV_OMITS_BOM;USE_CURL_FOR_IMAP_SEND;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;NO_GETTEXT;GIT_HOST_CPU=\"AMD64\";SHA256_BLK;INTERNAL_QSORT;RUNTIME_PREFIX;NO_OPENSSL;SHA1_DC;SHA1DC_NO_STANDARD_INCLUDES;SHA1DC_INIT_SAFE_HASH_DEFAULT=0;SHA1DC_CUSTOM_INCLUDE_SHA1_C=\"git-compat-util.h\";SHA1DC_CUSTOM_INCLUDE_UBC_CHECK_C=\"git-compat-util.h\";PAGER_ENV=\"LESS=FRX LV=-c\";GIT_EXEC_PATH=\"libexec/git-core\";GIT_LOCALE_PATH=\"share/locale\";GIT_MAN_PATH=\"share/man\";GIT_INFO_PATH=\"share/info\";GIT_HTML_PATH=\"share/doc/git-doc\";DEFAULT_HELP_FORMAT=\"html\";DEFAULT_GIT_TEMPLATE_DIR=\"share/git-core/templates\";BINDIR=\"bin\";FALLBACK_RUNTIME_PREFIX=\"/mingw64\";ETC_GITATTRIBUTES=\"../etc/gitattributes\";ETC_GITCONFIG=\"../etc/gitconfig\";_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;HAVE_ALLOCA_H;NO_POSIX_GOODIES;NATIVE_CRLF;NO_UNIX_SOCKETS;_CONSOLE;DETECT_MSYS_TTY;STRIP_EXTENSION=\".exe\";NO_SYMLINK_HEAD;UNRELIABLE_FSTAT;NOGDI;OBJECT_CREATION_MODE=1;__USE_MINGW_ANSI_STDIO=0;USE_NED_ALLOCATOR;OVERRIDE_STRDUP;MMAP_PREVENTS_DELETE;USE_WIN32_MMAP;HAVE_WPGMPTR;ENSURE_MSYSTEM_IS_SET;HAVE_RTLGENRANDOM;SUPPORTS_SIMPLE_IPC;HAVE_FSMONITOR_DAEMON_BACKEND;HAVE_FSMONITOR_OS_SETTINGS;NO_LIBGEN_H;NO_SYS_SELECT_H;NO_SYS_POLL_H;NO_POLL_H;NO_STRCASESTR;NO_MEMMEM;NO_STRLCPY;NO_SETENV;NO_MKDTEMP;NO_POLL;NO_PREAD;NO_ST_BLOCKS_IN_STRUCT_STAT;NO_REGEX;NO_MBSUPPORT;GAWK;ICONV_OMITS_BOM;USE_CURL_FOR_IMAP_SEND;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary C:/Users/<USER>/Desktop/workspace/tools/git/git/build/t/unit-tests/bin/t-reftable-record.exe -installedDir C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>Release\unit-test-lib.lib;Release\clar-test-lib.lib;Release\libgit.lib;Release\xdiff.lib;Release\reftable.lib;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\lib\zlib.lib;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\debug\lib\iconv.lib;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\debug\lib\charset.lib;ws2_32.lib;ntdll.lib;git.res;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4217 /IGNORE:4049 invalidcontinue.obj</AdditionalOptions>
      <EntryPointSymbol>wmainCRTStartup</EntryPointSymbol>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/workspace/tools/git/git/build/Release/t-reftable-record.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/workspace/tools/git/git/build/t/unit-tests/bin/t-reftable-record.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard_C>stdc11</LanguageStandard_C>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;NO_GETTEXT;GIT_HOST_CPU="AMD64";SHA256_BLK;INTERNAL_QSORT;RUNTIME_PREFIX;NO_OPENSSL;SHA1_DC;SHA1DC_NO_STANDARD_INCLUDES;SHA1DC_INIT_SAFE_HASH_DEFAULT=0;SHA1DC_CUSTOM_INCLUDE_SHA1_C="git-compat-util.h";SHA1DC_CUSTOM_INCLUDE_UBC_CHECK_C="git-compat-util.h";PAGER_ENV="LESS=FRX LV=-c";GIT_EXEC_PATH="libexec/git-core";GIT_LOCALE_PATH="share/locale";GIT_MAN_PATH="share/man";GIT_INFO_PATH="share/info";GIT_HTML_PATH="share/doc/git-doc";DEFAULT_HELP_FORMAT="html";DEFAULT_GIT_TEMPLATE_DIR="share/git-core/templates";BINDIR="bin";FALLBACK_RUNTIME_PREFIX="/mingw64";ETC_GITATTRIBUTES="../etc/gitattributes";ETC_GITCONFIG="../etc/gitconfig";_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;HAVE_ALLOCA_H;NO_POSIX_GOODIES;NATIVE_CRLF;NO_UNIX_SOCKETS;_CONSOLE;DETECT_MSYS_TTY;STRIP_EXTENSION=".exe";NO_SYMLINK_HEAD;UNRELIABLE_FSTAT;NOGDI;OBJECT_CREATION_MODE=1;__USE_MINGW_ANSI_STDIO=0;USE_NED_ALLOCATOR;OVERRIDE_STRDUP;MMAP_PREVENTS_DELETE;USE_WIN32_MMAP;HAVE_WPGMPTR;ENSURE_MSYSTEM_IS_SET;HAVE_RTLGENRANDOM;SUPPORTS_SIMPLE_IPC;HAVE_FSMONITOR_DAEMON_BACKEND;HAVE_FSMONITOR_OS_SETTINGS;NO_LIBGEN_H;NO_SYS_SELECT_H;NO_SYS_POLL_H;NO_POLL_H;NO_STRCASESTR;NO_MEMMEM;NO_STRLCPY;NO_SETENV;NO_MKDTEMP;NO_POLL;NO_PREAD;NO_ST_BLOCKS_IN_STRUCT_STAT;NO_REGEX;NO_MBSUPPORT;GAWK;ICONV_OMITS_BOM;USE_CURL_FOR_IMAP_SEND;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;NO_GETTEXT;GIT_HOST_CPU=\"AMD64\";SHA256_BLK;INTERNAL_QSORT;RUNTIME_PREFIX;NO_OPENSSL;SHA1_DC;SHA1DC_NO_STANDARD_INCLUDES;SHA1DC_INIT_SAFE_HASH_DEFAULT=0;SHA1DC_CUSTOM_INCLUDE_SHA1_C=\"git-compat-util.h\";SHA1DC_CUSTOM_INCLUDE_UBC_CHECK_C=\"git-compat-util.h\";PAGER_ENV=\"LESS=FRX LV=-c\";GIT_EXEC_PATH=\"libexec/git-core\";GIT_LOCALE_PATH=\"share/locale\";GIT_MAN_PATH=\"share/man\";GIT_INFO_PATH=\"share/info\";GIT_HTML_PATH=\"share/doc/git-doc\";DEFAULT_HELP_FORMAT=\"html\";DEFAULT_GIT_TEMPLATE_DIR=\"share/git-core/templates\";BINDIR=\"bin\";FALLBACK_RUNTIME_PREFIX=\"/mingw64\";ETC_GITATTRIBUTES=\"../etc/gitattributes\";ETC_GITCONFIG=\"../etc/gitconfig\";_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;HAVE_ALLOCA_H;NO_POSIX_GOODIES;NATIVE_CRLF;NO_UNIX_SOCKETS;_CONSOLE;DETECT_MSYS_TTY;STRIP_EXTENSION=\".exe\";NO_SYMLINK_HEAD;UNRELIABLE_FSTAT;NOGDI;OBJECT_CREATION_MODE=1;__USE_MINGW_ANSI_STDIO=0;USE_NED_ALLOCATOR;OVERRIDE_STRDUP;MMAP_PREVENTS_DELETE;USE_WIN32_MMAP;HAVE_WPGMPTR;ENSURE_MSYSTEM_IS_SET;HAVE_RTLGENRANDOM;SUPPORTS_SIMPLE_IPC;HAVE_FSMONITOR_DAEMON_BACKEND;HAVE_FSMONITOR_OS_SETTINGS;NO_LIBGEN_H;NO_SYS_SELECT_H;NO_SYS_POLL_H;NO_POLL_H;NO_STRCASESTR;NO_MEMMEM;NO_STRLCPY;NO_SETENV;NO_MKDTEMP;NO_POLL;NO_PREAD;NO_ST_BLOCKS_IN_STRUCT_STAT;NO_REGEX;NO_MBSUPPORT;GAWK;ICONV_OMITS_BOM;USE_CURL_FOR_IMAP_SEND;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary C:/Users/<USER>/Desktop/workspace/tools/git/git/build/t/unit-tests/bin/MinSizeRel/t-reftable-record.exe -installedDir C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>MinSizeRel\unit-test-lib.lib;MinSizeRel\clar-test-lib.lib;MinSizeRel\libgit.lib;MinSizeRel\xdiff.lib;MinSizeRel\reftable.lib;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\lib\zlib.lib;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\debug\lib\iconv.lib;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\debug\lib\charset.lib;ws2_32.lib;ntdll.lib;git.res;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4217 /IGNORE:4049 invalidcontinue.obj</AdditionalOptions>
      <EntryPointSymbol>wmainCRTStartup</EntryPointSymbol>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/workspace/tools/git/git/build/MinSizeRel/t-reftable-record.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/workspace/tools/git/git/build/t/unit-tests/bin/MinSizeRel/t-reftable-record.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard_C>stdc11</LanguageStandard_C>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;NO_GETTEXT;GIT_HOST_CPU="AMD64";SHA256_BLK;INTERNAL_QSORT;RUNTIME_PREFIX;NO_OPENSSL;SHA1_DC;SHA1DC_NO_STANDARD_INCLUDES;SHA1DC_INIT_SAFE_HASH_DEFAULT=0;SHA1DC_CUSTOM_INCLUDE_SHA1_C="git-compat-util.h";SHA1DC_CUSTOM_INCLUDE_UBC_CHECK_C="git-compat-util.h";PAGER_ENV="LESS=FRX LV=-c";GIT_EXEC_PATH="libexec/git-core";GIT_LOCALE_PATH="share/locale";GIT_MAN_PATH="share/man";GIT_INFO_PATH="share/info";GIT_HTML_PATH="share/doc/git-doc";DEFAULT_HELP_FORMAT="html";DEFAULT_GIT_TEMPLATE_DIR="share/git-core/templates";BINDIR="bin";FALLBACK_RUNTIME_PREFIX="/mingw64";ETC_GITATTRIBUTES="../etc/gitattributes";ETC_GITCONFIG="../etc/gitconfig";_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;HAVE_ALLOCA_H;NO_POSIX_GOODIES;NATIVE_CRLF;NO_UNIX_SOCKETS;_CONSOLE;DETECT_MSYS_TTY;STRIP_EXTENSION=".exe";NO_SYMLINK_HEAD;UNRELIABLE_FSTAT;NOGDI;OBJECT_CREATION_MODE=1;__USE_MINGW_ANSI_STDIO=0;USE_NED_ALLOCATOR;OVERRIDE_STRDUP;MMAP_PREVENTS_DELETE;USE_WIN32_MMAP;HAVE_WPGMPTR;ENSURE_MSYSTEM_IS_SET;HAVE_RTLGENRANDOM;SUPPORTS_SIMPLE_IPC;HAVE_FSMONITOR_DAEMON_BACKEND;HAVE_FSMONITOR_OS_SETTINGS;NO_LIBGEN_H;NO_SYS_SELECT_H;NO_SYS_POLL_H;NO_POLL_H;NO_STRCASESTR;NO_MEMMEM;NO_STRLCPY;NO_SETENV;NO_MKDTEMP;NO_POLL;NO_PREAD;NO_ST_BLOCKS_IN_STRUCT_STAT;NO_REGEX;NO_MBSUPPORT;GAWK;ICONV_OMITS_BOM;USE_CURL_FOR_IMAP_SEND;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;NO_GETTEXT;GIT_HOST_CPU=\"AMD64\";SHA256_BLK;INTERNAL_QSORT;RUNTIME_PREFIX;NO_OPENSSL;SHA1_DC;SHA1DC_NO_STANDARD_INCLUDES;SHA1DC_INIT_SAFE_HASH_DEFAULT=0;SHA1DC_CUSTOM_INCLUDE_SHA1_C=\"git-compat-util.h\";SHA1DC_CUSTOM_INCLUDE_UBC_CHECK_C=\"git-compat-util.h\";PAGER_ENV=\"LESS=FRX LV=-c\";GIT_EXEC_PATH=\"libexec/git-core\";GIT_LOCALE_PATH=\"share/locale\";GIT_MAN_PATH=\"share/man\";GIT_INFO_PATH=\"share/info\";GIT_HTML_PATH=\"share/doc/git-doc\";DEFAULT_HELP_FORMAT=\"html\";DEFAULT_GIT_TEMPLATE_DIR=\"share/git-core/templates\";BINDIR=\"bin\";FALLBACK_RUNTIME_PREFIX=\"/mingw64\";ETC_GITATTRIBUTES=\"../etc/gitattributes\";ETC_GITCONFIG=\"../etc/gitconfig\";_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_DEPRECATE;HAVE_ALLOCA_H;NO_POSIX_GOODIES;NATIVE_CRLF;NO_UNIX_SOCKETS;_CONSOLE;DETECT_MSYS_TTY;STRIP_EXTENSION=\".exe\";NO_SYMLINK_HEAD;UNRELIABLE_FSTAT;NOGDI;OBJECT_CREATION_MODE=1;__USE_MINGW_ANSI_STDIO=0;USE_NED_ALLOCATOR;OVERRIDE_STRDUP;MMAP_PREVENTS_DELETE;USE_WIN32_MMAP;HAVE_WPGMPTR;ENSURE_MSYSTEM_IS_SET;HAVE_RTLGENRANDOM;SUPPORTS_SIMPLE_IPC;HAVE_FSMONITOR_DAEMON_BACKEND;HAVE_FSMONITOR_OS_SETTINGS;NO_LIBGEN_H;NO_SYS_SELECT_H;NO_SYS_POLL_H;NO_POLL_H;NO_STRCASESTR;NO_MEMMEM;NO_STRLCPY;NO_SETENV;NO_MKDTEMP;NO_POLL;NO_PREAD;NO_ST_BLOCKS_IN_STRUCT_STAT;NO_REGEX;NO_MBSUPPORT;GAWK;ICONV_OMITS_BOM;USE_CURL_FOR_IMAP_SEND;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary C:/Users/<USER>/Desktop/workspace/tools/git/git/build/t/unit-tests/bin/RelWithDebInfo/t-reftable-record.exe -installedDir C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>RelWithDebInfo\unit-test-lib.lib;RelWithDebInfo\clar-test-lib.lib;RelWithDebInfo\libgit.lib;RelWithDebInfo\xdiff.lib;RelWithDebInfo\reftable.lib;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\lib\zlib.lib;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\debug\lib\iconv.lib;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\debug\lib\charset.lib;ws2_32.lib;ntdll.lib;git.res;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64 /IGNORE:4217 /IGNORE:4049 invalidcontinue.obj</AdditionalOptions>
      <EntryPointSymbol>wmainCRTStartup</EntryPointSymbol>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/workspace/tools/git/git/build/RelWithDebInfo/t-reftable-record.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/workspace/tools/git/git/build/t/unit-tests/bin/RelWithDebInfo/t-reftable-record.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -SC:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems -BC:/Users/<USER>/Desktop/workspace/tools/git/git/build --check-stamp-file C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTest.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestUseLaunchers.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckFunctionExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckStructHasMember.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckTypeSize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\DartConfiguration.tcl.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindEXPAT.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindIconv.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPkgConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\expat\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\iconv\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\scripts\buildsystems\vcpkg.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\description;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\applypatch-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\fsmonitor-watchman.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\post-update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-applypatch.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-merge-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-push.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-rebase.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-receive.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\prepare-commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\push-to-checkout.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\sendemail-validate.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\info\exclude;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -SC:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems -BC:/Users/<USER>/Desktop/workspace/tools/git/git/build --check-stamp-file C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTest.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestUseLaunchers.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckFunctionExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckStructHasMember.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckTypeSize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\DartConfiguration.tcl.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindEXPAT.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindIconv.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPkgConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\expat\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\iconv\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\scripts\buildsystems\vcpkg.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\description;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\applypatch-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\fsmonitor-watchman.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\post-update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-applypatch.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-merge-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-push.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-rebase.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-receive.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\prepare-commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\push-to-checkout.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\sendemail-validate.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\info\exclude;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -SC:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems -BC:/Users/<USER>/Desktop/workspace/tools/git/git/build --check-stamp-file C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTest.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestUseLaunchers.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckFunctionExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckStructHasMember.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckTypeSize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\DartConfiguration.tcl.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindEXPAT.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindIconv.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPkgConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\expat\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\iconv\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\scripts\buildsystems\vcpkg.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\description;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\applypatch-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\fsmonitor-watchman.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\post-update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-applypatch.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-merge-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-push.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-rebase.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-receive.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\prepare-commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\push-to-checkout.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\sendemail-validate.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\info\exclude;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -SC:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems -BC:/Users/<USER>/Desktop/workspace/tools/git/git/build --check-stamp-file C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTest.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestUseLaunchers.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckFunctionExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckStructHasMember.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckTypeSize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\DartConfiguration.tcl.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindEXPAT.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindIconv.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPkgConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\expat\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\iconv\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\scripts\buildsystems\vcpkg.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\description;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\applypatch-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\fsmonitor-watchman.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\post-update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-applypatch.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-merge-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-push.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-rebase.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-receive.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\prepare-commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\push-to-checkout.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\sendemail-validate.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\info\exclude;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\t-reftable-record.c" />
    <Object Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\common-main.dir\$(Configuration)\common-main.obj" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\ZERO_CHECK.vcxproj">
      <Project>{1EA13B83-14DD-3A1E-96AC-46FB2F067586}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\clar-test-lib.vcxproj">
      <Project>{71ACED41-0C29-3168-9CC7-114374361EF8}</Project>
      <Name>clar-test-lib</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\common-main.vcxproj">
      <Project>{7A678348-60C4-3700-8665-8A6936A7183E}</Project>
      <Name>common-main</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\libgit.vcxproj">
      <Project>{B69D294C-6DA3-344F-BB7B-6B10BBFAF7FE}</Project>
      <Name>libgit</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\reftable.vcxproj">
      <Project>{AC6E8DE5-1103-3DBB-B4A8-64B92CD57D7B}</Project>
      <Name>reftable</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\unit-test-lib.vcxproj">
      <Project>{F0D03342-292C-3522-B0A5-D0B8F907F04B}</Project>
      <Name>unit-test-lib</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\xdiff.vcxproj">
      <Project>{A90BC02A-EEDB-3B5A-AA20-958EDB5625AD}</Project>
      <Name>xdiff</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>