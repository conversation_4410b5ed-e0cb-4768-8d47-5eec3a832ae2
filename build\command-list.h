/* Automatically generated by generate-cmdlist.sh */
	struct cmdname_help {
		const char *name;
		const char *help;
		uint32_t category;
	};
	

/* Command categories */
#define CAT_ancillaryinterrogators (1UL << 0)
#define CAT_ancillarymanipulators (1UL << 1)
#define CAT_complete (1UL << 2)
#define CAT_developerinterfaces (1UL << 3)
#define CAT_foreignscminterface (1UL << 4)
#define CAT_guide (1UL << 5)
#define CAT_history (1UL << 6)
#define CAT_info (1UL << 7)
#define CAT_init (1UL << 8)
#define CAT_mainporcelain (1UL << 9)
#define CAT_plumbinginterrogators (1UL << 10)
#define CAT_plumbingmanipulators (1UL << 11)
#define CAT_purehelpers (1UL << 12)
#define CAT_remote (1UL << 13)
#define CAT_synchelpers (1UL << 14)
#define CAT_synchingrepositories (1UL << 15)
#define CAT_userinterfaces (1UL << 16)
#define CAT_worktree (1UL << 17)


/* Category names */
static const char *category_names[] = {
	"ancillaryinterrogators", /* (1UL << 0) */
	"ancillarymanipulators", /* (1UL << 1) */
	"complete", /* (1UL << 2) */
	"developerinterfaces", /* (1UL << 3) */
	"foreignscminterface", /* (1UL << 4) */
	"guide", /* (1UL << 5) */
	"history", /* (1UL << 6) */
	"info", /* (1UL << 7) */
	"init", /* (1UL << 8) */
	"mainporcelain", /* (1UL << 9) */
	"plumbinginterrogators", /* (1UL << 10) */
	"plumbingmanipulators", /* (1UL << 11) */
	"purehelpers", /* (1UL << 12) */
	"remote", /* (1UL << 13) */
	"synchelpers", /* (1UL << 14) */
	"synchingrepositories", /* (1UL << 15) */
	"userinterfaces", /* (1UL << 16) */
	"worktree", /* (1UL << 17) */
	NULL
};

static struct cmdname_help command_list[] = {
	{ "git-add", N_("Add file contents to the index"), 0 | CAT_mainporcelain | CAT_worktree },
	{ "git-am", N_("Apply a series of patches from a mailbox"), 0 | CAT_mainporcelain },
	{ "git-annotate", N_("Annotate file lines with commit information"), 0 | CAT_ancillaryinterrogators },
	{ "git-apply", N_("Apply a patch to files and/or to the index"), 0 | CAT_plumbingmanipulators | CAT_complete },
	{ "git-archimport", N_("Import a GNU Arch repository into Git"), 0 | CAT_foreignscminterface },
	{ "git-archive", N_("Create an archive of files from a named tree"), 0 | CAT_mainporcelain },
	{ "git-backfill", N_("Download missing objects in a partial clone"), 0 | CAT_mainporcelain | CAT_history },
	{ "git-bisect", N_("Use binary search to find the commit that introduced a bug"), 0 | CAT_mainporcelain | CAT_info },
	{ "git-blame", N_("Show what revision and author last modified each line of a file"), 0 | CAT_ancillaryinterrogators | CAT_complete },
	{ "git-branch", N_("List, create, or delete branches"), 0 | CAT_mainporcelain | CAT_history },
	{ "git-bugreport", N_("Collect information for user to file a bug report"), 0 | CAT_ancillaryinterrogators },
	{ "git-bundle", N_("Move objects and refs by archive"), 0 | CAT_mainporcelain },
	{ "git-cat-file", N_("Provide contents or details of repository objects"), 0 | CAT_plumbinginterrogators },
	{ "git-check-attr", N_("Display gitattributes information"), 0 | CAT_purehelpers },
	{ "git-check-ignore", N_("Debug gitignore / exclude files"), 0 | CAT_purehelpers },
	{ "git-check-mailmap", N_("Show canonical names and email addresses of contacts"), 0 | CAT_purehelpers },
	{ "git-check-ref-format", N_("Ensures that a reference name is well formed"), 0 | CAT_purehelpers },
	{ "git-checkout", N_("Switch branches or restore working tree files"), 0 | CAT_mainporcelain },
	{ "git-checkout-index", N_("Copy files from the index to the working tree"), 0 | CAT_plumbingmanipulators },
	{ "git-cherry", N_("Find commits yet to be applied to upstream"), 0 | CAT_plumbinginterrogators | CAT_complete },
	{ "git-cherry-pick", N_("Apply the changes introduced by some existing commits"), 0 | CAT_mainporcelain },
	{ "git-citool", N_("Graphical alternative to git-commit"), 0 | CAT_mainporcelain },
	{ "git-clean", N_("Remove untracked files from the working tree"), 0 | CAT_mainporcelain },
	{ "git-clone", N_("Clone a repository into a new directory"), 0 | CAT_mainporcelain | CAT_init },
	{ "git-column", N_("Display data in columns"), 0 | CAT_purehelpers },
	{ "git-commit", N_("Record changes to the repository"), 0 | CAT_mainporcelain | CAT_history },
	{ "git-commit-graph", N_("Write and verify Git commit-graph files"), 0 | CAT_plumbingmanipulators },
	{ "git-commit-tree", N_("Create a new commit object"), 0 | CAT_plumbingmanipulators },
	{ "git-config", N_("Get and set repository or global options"), 0 | CAT_ancillarymanipulators | CAT_complete },
	{ "git-count-objects", N_("Count unpacked number of objects and their disk consumption"), 0 | CAT_ancillaryinterrogators },
	{ "git-credential", N_("Retrieve and store user credentials"), 0 | CAT_purehelpers },
	{ "git-credential-cache", N_("Helper to temporarily store passwords in memory"), 0 | CAT_purehelpers },
	{ "git-credential-store", N_("Helper to store credentials on disk"), 0 | CAT_purehelpers },
	{ "git-cvsexportcommit", N_("Export a single commit to a CVS checkout"), 0 | CAT_foreignscminterface },
	{ "git-cvsimport", N_("Salvage your data out of another SCM people love to hate"), 0 | CAT_foreignscminterface },
	{ "git-cvsserver", N_("A CVS server emulator for Git"), 0 | CAT_foreignscminterface },
	{ "git-daemon", N_("A really simple server for Git repositories"), 0 | CAT_synchingrepositories },
	{ "git-describe", N_("Give an object a human readable name based on an available ref"), 0 | CAT_mainporcelain },
	{ "git-diagnose", N_("Generate a zip archive of diagnostic information"), 0 | CAT_ancillaryinterrogators },
	{ "git-diff", N_("Show changes between commits, commit and working tree, etc"), 0 | CAT_mainporcelain | CAT_info },
	{ "git-diff-files", N_("Compares files in the working tree and the index"), 0 | CAT_plumbinginterrogators },
	{ "git-diff-index", N_("Compare a tree to the working tree or index"), 0 | CAT_plumbinginterrogators },
	{ "git-diff-pairs", N_("Compare the content and mode of provided blob pairs"), 0 | CAT_plumbinginterrogators },
	{ "git-diff-tree", N_("Compares the content and mode of blobs found via two tree objects"), 0 | CAT_plumbinginterrogators },
	{ "git-difftool", N_("Show changes using common diff tools"), 0 | CAT_ancillaryinterrogators | CAT_complete },
	{ "git-fast-export", N_("Git data exporter"), 0 | CAT_ancillarymanipulators },
	{ "git-fast-import", N_("Backend for fast Git data importers"), 0 | CAT_ancillarymanipulators },
	{ "git-fetch", N_("Download objects and refs from another repository"), 0 | CAT_mainporcelain | CAT_remote },
	{ "git-fetch-pack", N_("Receive missing objects from another repository"), 0 | CAT_synchingrepositories },
	{ "git-filter-branch", N_("Rewrite branches"), 0 | CAT_ancillarymanipulators },
	{ "git-fmt-merge-msg", N_("Produce a merge commit message"), 0 | CAT_purehelpers },
	{ "git-for-each-ref", N_("Output information on each ref"), 0 | CAT_plumbinginterrogators },
	{ "git-for-each-repo", N_("Run a Git command on a list of repositories"), 0 | CAT_plumbinginterrogators },
	{ "git-format-patch", N_("Prepare patches for e-mail submission"), 0 | CAT_mainporcelain },
	{ "git-fsck", N_("Verifies the connectivity and validity of the objects in the database"), 0 | CAT_ancillaryinterrogators | CAT_complete },
	{ "git-gc", N_("Cleanup unnecessary files and optimize the local repository"), 0 | CAT_mainporcelain },
	{ "git-get-tar-commit-id", N_("Extract commit ID from an archive created using git-archive"), 0 | CAT_plumbinginterrogators },
	{ "git-grep", N_("Print lines matching a pattern"), 0 | CAT_mainporcelain | CAT_info },
	{ "git-gui", N_("A portable graphical interface to Git"), 0 | CAT_mainporcelain },
	{ "git-hash-object", N_("Compute object ID and optionally create an object from a file"), 0 | CAT_plumbingmanipulators },
	{ "git-help", N_("Display help information about Git"), 0 | CAT_ancillaryinterrogators | CAT_complete },
	{ "git-hook", N_("Run git hooks"), 0 | CAT_purehelpers },
	{ "git-http-backend", N_("Server side implementation of Git over HTTP"), 0 | CAT_synchingrepositories },
	{ "git-http-fetch", N_("Download from a remote Git repository via HTTP"), 0 | CAT_synchelpers },
	{ "git-http-push", N_("Push objects over HTTP/DAV to another repository"), 0 | CAT_synchelpers },
	{ "git-imap-send", N_("Send a collection of patches from stdin to an IMAP folder"), 0 | CAT_foreignscminterface },
	{ "git-index-pack", N_("Build pack index file for an existing packed archive"), 0 | CAT_plumbingmanipulators },
	{ "git-init", N_("Create an empty Git repository or reinitialize an existing one"), 0 | CAT_mainporcelain | CAT_init },
	{ "git-instaweb", N_("Instantly browse your working repository in gitweb"), 0 | CAT_ancillaryinterrogators | CAT_complete },
	{ "git-interpret-trailers", N_("Add or parse structured information in commit messages"), 0 | CAT_purehelpers },
	{ "git-log", N_("Show commit logs"), 0 | CAT_mainporcelain | CAT_info },
	{ "git-ls-files", N_("Show information about files in the index and the working tree"), 0 | CAT_plumbinginterrogators },
	{ "git-ls-remote", N_("List references in a remote repository"), 0 | CAT_plumbinginterrogators },
	{ "git-ls-tree", N_("List the contents of a tree object"), 0 | CAT_plumbinginterrogators },
	{ "git-mailinfo", N_("Extracts patch and authorship from a single e-mail message"), 0 | CAT_purehelpers },
	{ "git-mailsplit", N_("Simple UNIX mbox splitter program"), 0 | CAT_purehelpers },
	{ "git-maintenance", N_("Run tasks to optimize Git repository data"), 0 | CAT_mainporcelain },
	{ "git-merge", N_("Join two or more development histories together"), 0 | CAT_mainporcelain | CAT_history },
	{ "git-merge-base", N_("Find as good common ancestors as possible for a merge"), 0 | CAT_plumbinginterrogators },
	{ "git-merge-file", N_("Run a three-way file merge"), 0 | CAT_plumbingmanipulators },
	{ "git-merge-index", N_("Run a merge for files needing merging"), 0 | CAT_plumbingmanipulators },
	{ "git-merge-one-file", N_("The standard helper program to use with git-merge-index"), 0 | CAT_purehelpers },
	{ "git-merge-tree", N_("Perform merge without touching index or working tree"), 0 | CAT_ancillaryinterrogators },
	{ "git-mergetool", N_("Run merge conflict resolution tools to resolve merge conflicts"), 0 | CAT_ancillarymanipulators | CAT_complete },
	{ "git-mktag", N_("Creates a tag object with extra validation"), 0 | CAT_plumbingmanipulators },
	{ "git-mktree", N_("Build a tree-object from ls-tree formatted text"), 0 | CAT_plumbingmanipulators },
	{ "git-multi-pack-index", N_("Write and verify multi-pack-indexes"), 0 | CAT_plumbingmanipulators },
	{ "git-mv", N_("Move or rename a file, a directory, or a symlink"), 0 | CAT_mainporcelain | CAT_worktree },
	{ "git-name-rev", N_("Find symbolic names for given revs"), 0 | CAT_plumbinginterrogators },
	{ "git-notes", N_("Add or inspect object notes"), 0 | CAT_mainporcelain },
	{ "git-p4", N_("Import from and submit to Perforce repositories"), 0 | CAT_foreignscminterface },
	{ "git-pack-objects", N_("Create a packed archive of objects"), 0 | CAT_plumbingmanipulators },
	{ "git-pack-redundant", N_("Find redundant pack files"), 0 | CAT_plumbinginterrogators },
	{ "git-pack-refs", N_("Pack heads and tags for efficient repository access"), 0 | CAT_ancillarymanipulators },
	{ "git-patch-id", N_("Compute unique ID for a patch"), 0 | CAT_purehelpers },
	{ "git-prune", N_("Prune all unreachable objects from the object database"), 0 | CAT_ancillarymanipulators | CAT_complete },
	{ "git-prune-packed", N_("Remove extra objects that are already in pack files"), 0 | CAT_plumbingmanipulators },
	{ "git-pull", N_("Fetch from and integrate with another repository or a local branch"), 0 | CAT_mainporcelain | CAT_remote },
	{ "git-push", N_("Update remote refs along with associated objects"), 0 | CAT_mainporcelain | CAT_remote },
	{ "git-quiltimport", N_("Applies a quilt patchset onto the current branch"), 0 | CAT_foreignscminterface },
	{ "git-range-diff", N_("Compare two commit ranges (e.g. two versions of a branch)"), 0 | CAT_mainporcelain },
	{ "git-read-tree", N_("Reads tree information into the index"), 0 | CAT_plumbingmanipulators },
	{ "git-rebase", N_("Reapply commits on top of another base tip"), 0 | CAT_mainporcelain | CAT_history },
	{ "git-receive-pack", N_("Receive what is pushed into the repository"), 0 | CAT_synchelpers },
	{ "git-reflog", N_("Manage reflog information"), 0 | CAT_ancillarymanipulators | CAT_complete },
	{ "git-refs", N_("Low-level access to refs"), 0 | CAT_ancillarymanipulators | CAT_complete },
	{ "git-remote", N_("Manage set of tracked repositories"), 0 | CAT_ancillarymanipulators | CAT_complete },
	{ "git-repack", N_("Pack unpacked objects in a repository"), 0 | CAT_ancillarymanipulators | CAT_complete },
	{ "git-replace", N_("Create, list, delete refs to replace objects"), 0 | CAT_ancillarymanipulators | CAT_complete },
	{ "git-replay", N_("EXPERIMENTAL: Replay commits on a new base, works with bare repos too"), 0 | CAT_plumbingmanipulators },
	{ "git-request-pull", N_("Generates a summary of pending changes"), 0 | CAT_foreignscminterface | CAT_complete },
	{ "git-rerere", N_("Reuse recorded resolution of conflicted merges"), 0 | CAT_ancillaryinterrogators },
	{ "git-reset", N_("Reset current HEAD to the specified state"), 0 | CAT_mainporcelain | CAT_history },
	{ "git-restore", N_("Restore working tree files"), 0 | CAT_mainporcelain | CAT_worktree },
	{ "git-rev-list", N_("Lists commit objects in reverse chronological order"), 0 | CAT_plumbinginterrogators },
	{ "git-rev-parse", N_("Pick out and massage parameters"), 0 | CAT_plumbinginterrogators },
	{ "git-revert", N_("Revert some existing commits"), 0 | CAT_mainporcelain },
	{ "git-rm", N_("Remove files from the working tree and from the index"), 0 | CAT_mainporcelain | CAT_worktree },
	{ "git-send-email", N_("Send a collection of patches as emails"), 0 | CAT_foreignscminterface | CAT_complete },
	{ "git-send-pack", N_("Push objects over Git protocol to another repository"), 0 | CAT_synchingrepositories },
	{ "git-sh-i18n", N_("Git's i18n setup code for shell scripts"), 0 | CAT_purehelpers },
	{ "git-sh-setup", N_("Common Git shell script setup code"), 0 | CAT_purehelpers },
	{ "git-shell", N_("Restricted login shell for Git-only SSH access"), 0 | CAT_synchelpers },
	{ "git-shortlog", N_("Summarize 'git log' output"), 0 | CAT_mainporcelain },
	{ "git-show", N_("Show various types of objects"), 0 | CAT_mainporcelain | CAT_info },
	{ "git-show-branch", N_("Show branches and their commits"), 0 | CAT_ancillaryinterrogators | CAT_complete },
	{ "git-show-index", N_("Show packed archive index"), 0 | CAT_plumbinginterrogators },
	{ "git-show-ref", N_("List references in a local repository"), 0 | CAT_plumbinginterrogators },
	{ "git-sparse-checkout", N_("Reduce your working tree to a subset of tracked files"), 0 | CAT_mainporcelain },
	{ "git-stage", N_("Add file contents to the staging area"), 0 | CAT_complete },
	{ "git-stash", N_("Stash the changes in a dirty working directory away"), 0 | CAT_mainporcelain },
	{ "git-status", N_("Show the working tree status"), 0 | CAT_mainporcelain | CAT_info },
	{ "git-stripspace", N_("Remove unnecessary whitespace"), 0 | CAT_purehelpers },
	{ "git-submodule", N_("Initialize, update or inspect submodules"), 0 | CAT_mainporcelain },
	{ "git-svn", N_("Bidirectional operation between a Subversion repository and Git"), 0 | CAT_foreignscminterface },
	{ "git-switch", N_("Switch branches"), 0 | CAT_mainporcelain | CAT_history },
	{ "git-symbolic-ref", N_("Read, modify and delete symbolic refs"), 0 | CAT_plumbingmanipulators },
	{ "git-tag", N_("Create, list, delete or verify a tag object signed with GPG"), 0 | CAT_mainporcelain | CAT_history },
	{ "git-unpack-file", N_("Creates a temporary file with a blob's contents"), 0 | CAT_plumbinginterrogators },
	{ "git-unpack-objects", N_("Unpack objects from a packed archive"), 0 | CAT_plumbingmanipulators },
	{ "git-update-index", N_("Register file contents in the working tree to the index"), 0 | CAT_plumbingmanipulators },
	{ "git-update-ref", N_("Update the object name stored in a ref safely"), 0 | CAT_plumbingmanipulators },
	{ "git-update-server-info", N_("Update auxiliary info file to help dumb servers"), 0 | CAT_synchingrepositories },
	{ "git-upload-archive", N_("Send archive back to git-archive"), 0 | CAT_synchelpers },
	{ "git-upload-pack", N_("Send objects packed back to git-fetch-pack"), 0 | CAT_synchelpers },
	{ "git-var", N_("Show a Git logical variable"), 0 | CAT_plumbinginterrogators },
	{ "git-verify-commit", N_("Check the GPG signature of commits"), 0 | CAT_ancillaryinterrogators },
	{ "git-verify-pack", N_("Validate packed Git archive files"), 0 | CAT_plumbinginterrogators },
	{ "git-verify-tag", N_("Check the GPG signature of tags"), 0 | CAT_ancillaryinterrogators },
	{ "git-version", N_("Display version information about Git"), 0 | CAT_ancillaryinterrogators },
	{ "git-whatchanged", N_("Show logs with differences each commit introduces"), 0 | CAT_ancillaryinterrogators | CAT_complete },
	{ "git-worktree", N_("Manage multiple working trees"), 0 | CAT_mainporcelain },
	{ "git-write-tree", N_("Create a tree object from the current index"), 0 | CAT_plumbingmanipulators },
	{ "gitattributes", N_("Defining attributes per path"), 0 | CAT_userinterfaces },
	{ "gitcli", N_("Git command-line interface and conventions"), 0 | CAT_userinterfaces },
	{ "gitcore-tutorial", N_("A Git core tutorial for developers"), 0 | CAT_guide },
	{ "gitcredentials", N_("Providing usernames and passwords to Git"), 0 | CAT_guide },
	{ "gitcvs-migration", N_("Git for CVS users"), 0 | CAT_guide },
	{ "gitdiffcore", N_("Tweaking diff output"), 0 | CAT_guide },
	{ "giteveryday", N_("A useful minimum set of commands for Everyday Git"), 0 | CAT_guide },
	{ "gitfaq", N_("Frequently asked questions about using Git"), 0 | CAT_guide },
	{ "gitformat-bundle", N_("The bundle file format"), 0 | CAT_developerinterfaces },
	{ "gitformat-chunk", N_("Chunk-based file formats"), 0 | CAT_developerinterfaces },
	{ "gitformat-commit-graph", N_("Git commit-graph format"), 0 | CAT_developerinterfaces },
	{ "gitformat-index", N_("Git index format"), 0 | CAT_developerinterfaces },
	{ "gitformat-pack", N_("Git pack format"), 0 | CAT_developerinterfaces },
	{ "gitformat-signature", N_("Git cryptographic signature formats"), 0 | CAT_developerinterfaces },
	{ "gitglossary", N_("A Git Glossary"), 0 | CAT_guide },
	{ "githooks", N_("Hooks used by Git"), 0 | CAT_userinterfaces },
	{ "gitignore", N_("Specifies intentionally untracked files to ignore"), 0 | CAT_userinterfaces },
	{ "gitk", N_("The Git repository browser"), 0 | CAT_mainporcelain },
	{ "gitmailmap", N_("Map author/committer names and/or E-Mail addresses"), 0 | CAT_userinterfaces },
	{ "gitmodules", N_("Defining submodule properties"), 0 | CAT_userinterfaces },
	{ "gitnamespaces", N_("Git namespaces"), 0 | CAT_guide },
	{ "gitprotocol-capabilities", N_("Protocol v0 and v1 capabilities"), 0 | CAT_developerinterfaces },
	{ "gitprotocol-common", N_("Things common to various protocols"), 0 | CAT_developerinterfaces },
	{ "gitprotocol-http", N_("Git HTTP-based protocols"), 0 | CAT_developerinterfaces },
	{ "gitprotocol-pack", N_("How packs are transferred over-the-wire"), 0 | CAT_developerinterfaces },
	{ "gitprotocol-v2", N_("Git Wire Protocol, Version 2"), 0 | CAT_developerinterfaces },
	{ "gitremote-helpers", N_("Helper programs to interact with remote repositories"), 0 | CAT_guide },
	{ "gitrepository-layout", N_("Git Repository Layout"), 0 | CAT_userinterfaces },
	{ "gitrevisions", N_("Specifying revisions and ranges for Git"), 0 | CAT_userinterfaces },
	{ "gitsubmodules", N_("Mounting one repository inside another"), 0 | CAT_guide },
	{ "gittutorial", N_("A tutorial introduction to Git"), 0 | CAT_guide },
	{ "gittutorial-2", N_("A tutorial introduction to Git: part two"), 0 | CAT_guide },
	{ "gitweb", N_("Git web interface (web frontend to Git repositories)"), 0 | CAT_ancillaryinterrogators },
	{ "gitworkflows", N_("An overview of recommended workflows with Git"), 0 | CAT_guide },
	{ "scalar", N_("A tool for managing large Git repositories"), 0 | CAT_mainporcelain },
};
