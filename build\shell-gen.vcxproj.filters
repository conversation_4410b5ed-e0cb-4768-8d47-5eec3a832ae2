﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\b0f885988ecc73d15da5f717fc823e7a\git-difftool--helper.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\b0f885988ecc73d15da5f717fc823e7a\git-filter-branch.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\b0f885988ecc73d15da5f717fc823e7a\git-instaweb.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\b0f885988ecc73d15da5f717fc823e7a\git-merge-octopus.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\b0f885988ecc73d15da5f717fc823e7a\git-merge-one-file.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\b0f885988ecc73d15da5f717fc823e7a\git-merge-resolve.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\b0f885988ecc73d15da5f717fc823e7a\git-mergetool.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\b0f885988ecc73d15da5f717fc823e7a\git-mergetool--lib.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\b0f885988ecc73d15da5f717fc823e7a\git-quiltimport.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\b0f885988ecc73d15da5f717fc823e7a\git-request-pull.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\b0f885988ecc73d15da5f717fc823e7a\git-sh-i18n.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\b0f885988ecc73d15da5f717fc823e7a\git-sh-setup.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\b0f885988ecc73d15da5f717fc823e7a\git-submodule.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\b0f885988ecc73d15da5f717fc823e7a\git-web--browse.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\15e124c2f35056f23304497decf09d62\shell-gen.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\shell-gen" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{FD4F856E-57BF-3C53-A628-8AAFDE1B7E8A}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
