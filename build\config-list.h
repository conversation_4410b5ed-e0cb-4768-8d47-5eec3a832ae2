/* Automatically generated by generate-configlist.sh */


static const char *config_name_list[] = {
	"add.ignoreErrors",
	"advice.*",
	"alias.*",
	"am.keepcr",
	"am.threeWay",
	"apply.ignoreWhitespace",
	"apply.whitespace",
	"attr.tree",
	"author.email",
	"author.name",
	"bitmapPseudoMerge.<name>.decay",
	"bitmapPseudoMerge.<name>.maxMerges",
	"bitmapPseudoMerge.<name>.pattern",
	"bitmapPseudoMerge.<name>.sampleRate",
	"bitmapPseudoMerge.<name>.stableSize",
	"bitmapPseudoMerge.<name>.stableThreshold",
	"bitmapPseudoMerge.<name>.threshold",
	"blame.blankBoundary",
	"blame.coloring",
	"blame.date",
	"blame.ignoreRevsFile",
	"blame.markIgnoredLines",
	"blame.markUnblamableLines",
	"blame.showEmail",
	"blame.showRoot",
	"branch.<name>.description",
	"branch.<name>.merge",
	"branch.<name>.mergeOptions",
	"branch.<name>.pushRemote",
	"branch.<name>.rebase",
	"branch.<name>.remote",
	"branch.autoSetupMerge",
	"branch.autoSetupRebase",
	"branch.sort",
	"browser.<tool>.cmd",
	"browser.<tool>.path",
	"bundle.*",
	"bundle.<id>.*",
	"bundle.<id>.uri",
	"bundle.heuristic",
	"bundle.mode",
	"bundle.version",
	"checkout.defaultRemote",
	"checkout.guess",
	"checkout.thresholdForParallelism",
	"checkout.workers",
	"clean.requireForce",
	"clone.defaultRemoteName",
	"clone.filterSubmodules",
	"clone.rejectShallow",
	"color.advice",
	"color.advice.hint",
	"color.blame.highlightRecent",
	"color.blame.repeatedLines",
	"color.branch",
	"color.branch.<slot>",
	"color.decorate.<slot>",
	"color.diff",
	"color.diff.<slot>",
	"color.grep",
	"color.grep.<slot>",
	"color.interactive",
	"color.interactive.<slot>",
	"color.pager",
	"color.push",
	"color.push.error",
	"color.remote",
	"color.remote.<slot>",
	"color.showBranch",
	"color.status",
	"color.status.<slot>",
	"color.transport",
	"color.transport.rejected",
	"color.ui",
	"column.branch",
	"column.clean",
	"column.status",
	"column.tag",
	"column.ui",
	"commit.cleanup",
	"commit.gpgSign",
	"commit.status",
	"commit.template",
	"commit.verbose",
	"commitGraph.changedPathsVersion",
	"commitGraph.generationVersion",
	"commitGraph.maxNewFilters",
	"commitGraph.readChangedPaths",
	"committer.email",
	"committer.name",
	"completion.commands",
	"core.abbrev",
	"core.alternateRefsCommand",
	"core.alternateRefsPrefixes",
	"core.askPass",
	"core.attributesFile",
	"core.autocrlf",
	"core.bare",
	"core.bigFileThreshold",
	"core.checkRoundtripEncoding",
	"core.checkStat",
	"core.commentChar",
	"core.commentString",
	"core.commitGraph",
	"core.compression",
	"core.createObject",
	"core.deltaBaseCacheLimit",
	"core.editor",
	"core.eol",
	"core.excludesFile",
	"core.fileMode",
	"core.filesRefLockTimeout",
	"core.fsmonitor",
	"core.fsmonitorHookVersion",
	"core.fsync",
	"core.fsyncMethod",
	"core.fsyncObjectFiles",
	"core.gitProxy",
	"core.hideDotFiles",
	"core.hooksPath",
	"core.ignoreCase",
	"core.ignoreStat",
	"core.logAllRefUpdates",
	"core.looseCompression",
	"core.maxTreeDepth",
	"core.multiPackIndex",
	"core.notesRef",
	"core.packedGitLimit",
	"core.packedGitWindowSize",
	"core.packedRefsTimeout",
	"core.pager",
	"core.precomposeUnicode",
	"core.preferSymlinkRefs",
	"core.preloadIndex",
	"core.protectHFS",
	"core.protectNTFS",
	"core.quotePath",
	"core.repositoryFormatVersion",
	"core.restrictinheritedhandles",
	"core.safecrlf",
	"core.sharedRepository",
	"core.sparseCheckout",
	"core.sparseCheckoutCone",
	"core.splitIndex",
	"core.sshCommand",
	"core.symlinks",
	"core.trustctime",
	"core.unsetenvvars",
	"core.untrackedCache",
	"core.useReplaceRefs",
	"core.warnAmbiguousRefs",
	"core.whitespace",
	"core.worktree",
	"credential.<url>.*",
	"credential.helper",
	"credential.interactive",
	"credential.protectProtocol",
	"credential.sanitizePrompt",
	"credential.useHttpPath",
	"credential.username",
	"credentialCache.ignoreSIGHUP",
	"credentialStore.lockTimeoutMS",
	"diff.<driver>.binary",
	"diff.<driver>.cachetextconv",
	"diff.<driver>.command",
	"diff.<driver>.textconv",
	"diff.<driver>.trustExitCode",
	"diff.<driver>.wordRegex",
	"diff.<driver>.xfuncname",
	"diff.algorithm",
	"diff.autoRefreshIndex",
	"diff.colorMoved",
	"diff.colorMovedWS",
	"diff.context",
	"diff.dirstat",
	"diff.dstPrefix",
	"diff.external",
	"diff.guitool",
	"diff.ignoreSubmodules",
	"diff.indentHeuristic",
	"diff.interHunkContext",
	"diff.mnemonicPrefix",
	"diff.noPrefix",
	"diff.orderFile",
	"diff.relative",
	"diff.renameLimit",
	"diff.renames",
	"diff.srcPrefix",
	"diff.statGraphWidth",
	"diff.statNameWidth",
	"diff.submodule",
	"diff.suppressBlankEmpty",
	"diff.tool",
	"diff.trustExitCode",
	"diff.wordRegex",
	"diff.wsErrorHighlight",
	"difftool.<tool>.cmd",
	"difftool.<tool>.path",
	"difftool.guiDefault",
	"difftool.prompt",
	"difftool.trustExitCode",
	"extensions.*",
	"fastimport.unpackLimit",
	"feature.*",
	"feature.experimental",
	"feature.manyFiles",
	"fetch.all",
	"fetch.bundleCreationToken",
	"fetch.bundleURI",
	"fetch.fsck.<msg-id>",
	"fetch.fsck.skipList",
	"fetch.fsckObjects",
	"fetch.negotiationAlgorithm",
	"fetch.output",
	"fetch.parallel",
	"fetch.prune",
	"fetch.pruneTags",
	"fetch.recurseSubmodules",
	"fetch.showForcedUpdates",
	"fetch.unpackLimit",
	"fetch.writeCommitGraph",
	"filter.<driver>.clean",
	"filter.<driver>.smudge",
	"format.attach",
	"format.cc",
	"format.coverFromDescription",
	"format.coverLetter",
	"format.encodeEmailHeaders",
	"format.filenameMaxLength",
	"format.forceInBodyFrom",
	"format.from",
	"format.headers",
	"format.mboxrd",
	"format.noprefix",
	"format.notes",
	"format.numbered",
	"format.outputDirectory",
	"format.pretty",
	"format.signature",
	"format.signatureFile",
	"format.signOff",
	"format.subjectPrefix",
	"format.suffix",
	"format.thread",
	"format.to",
	"format.useAutoBase",
	"fsck.<msg-id>",
	"fsck.skipList",
	"fsmonitor.allowRemote",
	"fsmonitor.socketDir",
	"gc.<pattern>.reflogExpire",
	"gc.<pattern>.reflogExpireUnreachable",
	"gc.aggressiveDepth",
	"gc.aggressiveWindow",
	"gc.auto",
	"gc.autoDetach",
	"gc.autoPackLimit",
	"gc.bigPackThreshold",
	"gc.cruftPacks",
	"gc.logExpiry",
	"gc.maxCruftSize",
	"gc.packRefs",
	"gc.pruneExpire",
	"gc.recentObjectsHook",
	"gc.reflogExpire",
	"gc.reflogExpireUnreachable",
	"gc.repackFilter",
	"gc.repackFilterTo",
	"gc.rerereResolved",
	"gc.rerereUnresolved",
	"gc.worktreePruneExpire",
	"gc.writeCommitGraph",
	"gitcvs.allBinary",
	"gitcvs.commitMsgAnnotation",
	"gitcvs.dbDriver",
	"gitcvs.dbName",
	"gitcvs.dbPass",
	"gitcvs.dbTableNamePrefix",
	"gitcvs.dbUser",
	"gitcvs.enabled",
	"gitcvs.logFile",
	"gitcvs.usecrlfattr",
	"gitweb.avatar",
	"gitweb.blame",
	"gitweb.category",
	"gitweb.description",
	"gitweb.grep",
	"gitweb.highlight",
	"gitweb.owner",
	"gitweb.patches",
	"gitweb.pickaxe",
	"gitweb.remote_heads",
	"gitweb.showSizes",
	"gitweb.snapshot",
	"gitweb.url",
	"gpg.<format>.program",
	"gpg.format",
	"gpg.minTrustLevel",
	"gpg.program",
	"gpg.ssh.allowedSignersFile",
	"gpg.ssh.defaultKeyCommand",
	"gpg.ssh.revocationFile",
	"grep.column",
	"grep.extendedRegexp",
	"grep.fallbackToNoIndex",
	"grep.fullName",
	"grep.lineNumber",
	"grep.patternType",
	"grep.threads",
	"gui.blamehistoryctx",
	"gui.commitMsgWidth",
	"gui.copyBlameThreshold",
	"gui.diffContext",
	"gui.displayUntracked",
	"gui.encoding",
	"gui.fastCopyBlame",
	"gui.matchTrackingBranch",
	"gui.newBranchTemplate",
	"gui.pruneDuringFetch",
	"gui.spellingDictionary",
	"gui.trustmtime",
	"guitool.<name>.argPrompt",
	"guitool.<name>.cmd",
	"guitool.<name>.confirm",
	"guitool.<name>.needsFile",
	"guitool.<name>.noConsole",
	"guitool.<name>.noRescan",
	"guitool.<name>.prompt",
	"guitool.<name>.revPrompt",
	"guitool.<name>.revUnmerged",
	"guitool.<name>.title",
	"hasconfig:remote.*.url:",
	"help.autoCorrect",
	"help.browser",
	"help.format",
	"help.htmlPath",
	"http.<url>.*",
	"http.cookieFile",
	"http.curloptResolve",
	"http.delegation",
	"http.emptyAuth",
	"http.extraHeader",
	"http.followRedirects",
	"http.keepAliveCount",
	"http.keepAliveIdle",
	"http.keepAliveInterval",
	"http.lowSpeedLimit",
	"http.lowSpeedTime",
	"http.maxRequests",
	"http.minSessions",
	"http.noEPSV",
	"http.pinnedPubkey",
	"http.postBuffer",
	"http.proactiveAuth",
	"http.proxy",
	"http.proxyAuthMethod",
	"http.proxySSLCAInfo",
	"http.proxySSLCert",
	"http.proxySSLCertPasswordProtected",
	"http.proxySSLKey",
	"http.saveCookies",
	"http.schannelCheckRevoke",
	"http.schannelUseSSLCAInfo",
	"http.sslBackend",
	"http.sslCAInfo",
	"http.sslCAPath",
	"http.sslCert",
	"http.sslCertPasswordProtected",
	"http.sslCertType",
	"http.sslCipherList",
	"http.sslKey",
	"http.sslKeyType",
	"http.sslTry",
	"http.sslVerify",
	"http.sslVersion",
	"http.userAgent",
	"http.version",
	"i18n.commitEncoding",
	"i18n.logOutputEncoding",
	"imap.authMethod",
	"imap.folder",
	"imap.host",
	"imap.pass",
	"imap.port",
	"imap.preformattedHTML",
	"imap.sslverify",
	"imap.tunnel",
	"imap.user",
	"include.path",
	"includeIf.<condition>.path",
	"index.recordEndOfIndexEntries",
	"index.recordOffsetTable",
	"index.skipHash",
	"index.sparse",
	"index.threads",
	"index.version",
	"init.defaultBranch",
	"init.defaultObjectFormat",
	"init.defaultRefFormat",
	"init.templateDir",
	"instaweb.browser",
	"instaweb.httpd",
	"instaweb.local",
	"instaweb.modulePath",
	"instaweb.port",
	"interactive.diffFilter",
	"interactive.singleKey",
	"log.abbrevCommit",
	"log.date",
	"log.decorate",
	"log.diffMerges",
	"log.excludeDecoration",
	"log.follow",
	"log.graphColors",
	"log.initialDecorationSet",
	"log.mailmap",
	"log.showRoot",
	"log.showSignature",
	"lsrefs.unborn",
	"mailinfo.scissors",
	"mailmap.blob",
	"mailmap.file",
	"maintenance.<task>.enabled",
	"maintenance.<task>.schedule",
	"maintenance.auto",
	"maintenance.autoDetach",
	"maintenance.commit-graph.auto",
	"maintenance.incremental-repack.auto",
	"maintenance.loose-objects.auto",
	"maintenance.loose-objects.batchSize",
	"maintenance.reflog-expire.auto",
	"maintenance.rerere-gc.auto",
	"maintenance.strategy",
	"maintenance.worktree-prune.auto",
	"man.<tool>.cmd",
	"man.<tool>.path",
	"man.viewer",
	"merge.<driver>.driver",
	"merge.<driver>.name",
	"merge.<driver>.recursive",
	"merge.autoStash",
	"merge.branchdesc",
	"merge.conflictStyle",
	"merge.defaultToUpstream",
	"merge.directoryRenames",
	"merge.ff",
	"merge.guitool",
	"merge.log",
	"merge.renameLimit",
	"merge.renames",
	"merge.renormalize",
	"merge.stat",
	"merge.suppressDest",
	"merge.tool",
	"merge.verbosity",
	"merge.verifySignatures",
	"mergetool.<tool>.cmd",
	"mergetool.<tool>.hideResolved",
	"mergetool.<tool>.path",
	"mergetool.<tool>.trustExitCode",
	"mergetool.<variant>.layout",
	"mergetool.guiDefault",
	"mergetool.hideResolved",
	"mergetool.keepBackup",
	"mergetool.keepTemporaries",
	"mergetool.meld.hasOutput",
	"mergetool.meld.useAutoMerge",
	"mergetool.prompt",
	"mergetool.writeToTemp",
	"notes.<name>.mergeStrategy",
	"notes.displayRef",
	"notes.mergeStrategy",
	"notes.rewrite.<command>",
	"notes.rewriteMode",
	"notes.rewriteRef",
	"pack.allowPackReuse",
	"pack.compression",
	"pack.deltaCacheLimit",
	"pack.deltaCacheSize",
	"pack.depth",
	"pack.indexVersion",
	"pack.island",
	"pack.islandCore",
	"pack.packSizeLimit",
	"pack.preferBitmapTips",
	"pack.readReverseIndex",
	"pack.threads",
	"pack.useBitmapBoundaryTraversal",
	"pack.useBitmaps",
	"pack.useSparse",
	"pack.window",
	"pack.windowMemory",
	"pack.writeBitmapHashCache",
	"pack.writeBitmapLookupTable",
	"pack.writeReverseIndex",
	"pager.<cmd>",
	"pretty.<name>",
	"promisor.acceptFromServer",
	"promisor.advertise",
	"promisor.quiet",
	"protocol.<name>.allow",
	"protocol.allow",
	"protocol.version",
	"pull.ff",
	"pull.octopus",
	"pull.rebase",
	"pull.twohead",
	"push.autoSetupRemote",
	"push.default",
	"push.followTags",
	"push.gpgSign",
	"push.negotiate",
	"push.pushOption",
	"push.recurseSubmodules",
	"push.useBitmaps",
	"push.useForceIfIncludes",
	"rebase.abbreviateCommands",
	"rebase.autoSquash",
	"rebase.autoStash",
	"rebase.backend",
	"rebase.forkPoint",
	"rebase.instructionFormat",
	"rebase.maxLabelLength",
	"rebase.missingCommitsCheck",
	"rebase.rebaseMerges",
	"rebase.rescheduleFailedExec",
	"rebase.stat",
	"rebase.updateRefs",
	"receive.advertiseAtomic",
	"receive.advertisePushOptions",
	"receive.autogc",
	"receive.certNonceSeed",
	"receive.certNonceSlop",
	"receive.denyCurrentBranch",
	"receive.denyDeleteCurrent",
	"receive.denyDeletes",
	"receive.denyNonFastForwards",
	"receive.fsck.<msg-id>",
	"receive.fsck.skipList",
	"receive.fsckObjects",
	"receive.hideRefs",
	"receive.keepAlive",
	"receive.maxInputSize",
	"receive.procReceiveRefs",
	"receive.shallowUpdate",
	"receive.unpackLimit",
	"receive.updateServerInfo",
	"reftable.blockSize",
	"reftable.geometricFactor",
	"reftable.indexObjects",
	"reftable.lockTimeout",
	"reftable.restartInterval",
	"remote.<name>.fetch",
	"remote.<name>.followRemoteHEAD",
	"remote.<name>.mirror",
	"remote.<name>.partialclonefilter",
	"remote.<name>.promisor",
	"remote.<name>.proxy",
	"remote.<name>.proxyAuthMethod",
	"remote.<name>.prune",
	"remote.<name>.pruneTags",
	"remote.<name>.push",
	"remote.<name>.pushurl",
	"remote.<name>.receivepack",
	"remote.<name>.serverOption",
	"remote.<name>.skipDefaultUpdate",
	"remote.<name>.skipFetchAll",
	"remote.<name>.tagOpt",
	"remote.<name>.uploadpack",
	"remote.<name>.url",
	"remote.<name>.vcs",
	"remote.pushDefault",
	"remotes.<group>",
	"repack.cruftDepth",
	"repack.cruftThreads",
	"repack.cruftWindow",
	"repack.cruftWindowMemory",
	"repack.packKeptObjects",
	"repack.updateServerInfo",
	"repack.useDeltaBaseOffset",
	"repack.useDeltaIslands",
	"repack.writeBitmaps",
	"rerere.autoUpdate",
	"rerere.enabled",
	"revert.reference",
	"safe.bareRepository",
	"safe.directory",
	"sendemail.<identity>.*",
	"sendemail.aliasesFile",
	"sendemail.aliasFileType",
	"sendemail.annotate",
	"sendemail.bcc",
	"sendemail.cc",
	"sendemail.ccCmd",
	"sendemail.chainReplyTo",
	"sendemail.confirm",
	"sendemail.envelopeSender",
	"sendemail.forbidSendmailVariables",
	"sendemail.from",
	"sendemail.headerCmd",
	"sendemail.identity",
	"sendemail.mailmap",
	"sendemail.mailmap.blob",
	"sendemail.mailmap.file",
	"sendemail.multiEdit",
	"sendemail.signedOffByCc",
	"sendemail.smtpBatchSize",
	"sendemail.smtpDomain",
	"sendemail.smtpEncryption",
	"sendemail.smtpPass",
	"sendemail.smtpReloginDelay",
	"sendemail.smtpServer",
	"sendemail.smtpServerOption",
	"sendemail.smtpServerPort",
	"sendemail.smtpSSLCertPath",
	"sendemail.smtpUser",
	"sendemail.suppressCc",
	"sendemail.suppressFrom",
	"sendemail.thread",
	"sendemail.to",
	"sendemail.toCmd",
	"sendemail.transferEncoding",
	"sendemail.validate",
	"sendemail.xmailer",
	"sequence.editor",
	"showBranch.default",
	"sparse.expectFilesOutsideOfPatterns",
	"splitIndex.maxPercentChange",
	"splitIndex.sharedIndexExpire",
	"ssh.variant",
	"stash.showIncludeUntracked",
	"stash.showPatch",
	"stash.showStat",
	"status.aheadBehind",
	"status.branch",
	"status.displayCommentPrefix",
	"status.relativePaths",
	"status.renameLimit",
	"status.renames",
	"status.short",
	"status.showStash",
	"status.showUntrackedFiles",
	"status.submoduleSummary",
	"submodule.<name>.active",
	"submodule.<name>.branch",
	"submodule.<name>.fetchRecurseSubmodules",
	"submodule.<name>.ignore",
	"submodule.<name>.update",
	"submodule.<name>.url",
	"submodule.active",
	"submodule.alternateErrorStrategy",
	"submodule.alternateLocation",
	"submodule.fetchJobs",
	"submodule.propagateBranches",
	"submodule.recurse",
	"tag.forceSignAnnotated",
	"tag.gpgSign",
	"tag.sort",
	"tar.umask",
	"trace2.configParams",
	"trace2.destinationDebug",
	"trace2.envVars",
	"trace2.eventBrief",
	"trace2.eventNesting",
	"trace2.eventTarget",
	"trace2.maxFiles",
	"trace2.normalBrief",
	"trace2.normalTarget",
	"trace2.perfBrief",
	"trace2.perfTarget",
	"trailer.<keyAlias>.cmd",
	"trailer.<keyAlias>.command",
	"trailer.<keyAlias>.ifexists",
	"trailer.<keyAlias>.ifmissing",
	"trailer.<keyAlias>.key",
	"trailer.<keyAlias>.where",
	"trailer.ifexists",
	"trailer.ifmissing",
	"trailer.separators",
	"trailer.where",
	"transfer.advertiseObjectInfo",
	"transfer.advertiseSID",
	"transfer.bundleURI",
	"transfer.credentialsInUrl",
	"transfer.fsckObjects",
	"transfer.hideRefs",
	"transfer.unpackLimit",
	"uploadarchive.allowUnreachable",
	"uploadpack.allowAnySHA1InWant",
	"uploadpack.allowFilter",
	"uploadpack.allowReachableSHA1InWant",
	"uploadpack.allowRefInWant",
	"uploadpack.allowTipSHA1InWant",
	"uploadpack.hideRefs",
	"uploadpack.keepAlive",
	"uploadpack.packObjectsHook",
	"uploadpackfilter.<filter>.allow",
	"uploadpackfilter.allow",
	"uploadpackfilter.tree.maxDepth",
	"url.<base>.insteadOf",
	"url.<base>.pushInsteadOf",
	"user.email",
	"user.name",
	"user.signingKey",
	"user.useConfigOnly",
	"versionsort.suffix",
	"web.browser",
	"worktree.guessRemote",
	"worktree.useRelativePaths",
	NULL,
};
