
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:93 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:93 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
      鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:27:09銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\3.30.1\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdC.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\3.30.1\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\3.30.1\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:05.18
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/3.30.1/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:93 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-5cf442"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-5cf442"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-5cf442'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_6d74b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:27:16銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5cf442\\cmTC_6d74b.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_6d74b.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5cf442\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_6d74b.dir\\Debug\\cmTC_6d74b.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_6d74b.dir\\Debug\\cmTC_6d74b.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_6d74b.dir\\Debug\\cmTC_6d74b.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_6d74b.dir\\Debug\\\\" /Fd"cmTC_6d74b.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\compat\\vcbuild\\vcpkg\\downloads\\tools\\cmake-3.30.1-windows\\cmake-3.30.1-windows-i386\\share\\cmake-3.30\\Modules\\CMakeCCompilerABI.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_6d74b.dir\\Debug\\\\" /Fd"cmTC_6d74b.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\compat\\vcbuild\\vcpkg\\downloads\\tools\\cmake-3.30.1-windows\\cmake-3.30.1-windows-i386\\share\\cmake-3.30\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5cf442\\Debug\\cmTC_6d74b.exe" /INCREMENTAL /ILK:"cmTC_6d74b.dir\\Debug\\cmTC_6d74b.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-5cf442/Debug/cmTC_6d74b.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-5cf442/Debug/cmTC_6d74b.lib" /MACHINE:X64  /machine:x64 cmTC_6d74b.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_6d74b.vcxproj -> C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5cf442\\Debug\\cmTC_6d74b.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_6d74b.dir\\Debug\\cmTC_6d74b.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_6d74b.dir\\Debug\\cmTC_6d74b.tlog\\cmTC_6d74b.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5cf442\\cmTC_6d74b.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:23.97
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:93 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:93 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35209.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckIncludeFile.cmake:90 (try_compile)"
      - "CMakeLists.txt:317 (check_include_file)"
    checks:
      - "Looking for libgen.h"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-zxmfvz"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-zxmfvz"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_LIBGEN_H"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-zxmfvz'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_030aa.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:27:42銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zxmfvz\\cmTC_030aa.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_030aa.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zxmfvz\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_030aa.dir\\Debug\\cmTC_030aa.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_030aa.dir\\Debug\\cmTC_030aa.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_030aa.dir\\Debug\\cmTC_030aa.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_030aa.dir\\Debug\\\\" /Fd"cmTC_030aa.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zxmfvz\\CheckIncludeFile.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_030aa.dir\\Debug\\\\" /Fd"cmTC_030aa.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zxmfvz\\CheckIncludeFile.c"
          CheckIncludeFile.c
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zxmfvz\\CheckIncludeFile.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渓ibgen.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zxmfvz\\cmTC_030aa.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zxmfvz\\cmTC_030aa.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zxmfvz\\cmTC_030aa.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zxmfvz\\CheckIncludeFile.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渓ibgen.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-zxmfvz\\cmTC_030aa.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:02.10
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckIncludeFile.cmake:90 (try_compile)"
      - "CMakeLists.txt:323 (check_include_file)"
    checks:
      - "Looking for sys/sysinfo.h"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-tsi7up"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-tsi7up"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_SYSINFO"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-tsi7up'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_f217c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:27:45銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tsi7up\\cmTC_f217c.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_f217c.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tsi7up\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_f217c.dir\\Debug\\cmTC_f217c.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_f217c.dir\\Debug\\cmTC_f217c.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_f217c.dir\\Debug\\cmTC_f217c.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_f217c.dir\\Debug\\\\" /Fd"cmTC_f217c.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tsi7up\\CheckIncludeFile.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_f217c.dir\\Debug\\\\" /Fd"cmTC_f217c.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tsi7up\\CheckIncludeFile.c"
          CheckIncludeFile.c
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tsi7up\\CheckIncludeFile.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渟ys/sysinfo.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tsi7up\\cmTC_f217c.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tsi7up\\cmTC_f217c.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tsi7up\\cmTC_f217c.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tsi7up\\CheckIncludeFile.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渟ys/sysinfo.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-tsi7up\\cmTC_f217c.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.02
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "CMakeLists.txt:328 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_ALLOCA_H"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-bi8xo3"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-bi8xo3"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_ALLOCA_H"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-bi8xo3'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_f9708.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:27:46銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bi8xo3\\cmTC_f9708.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_f9708.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bi8xo3\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_f9708.dir\\Debug\\cmTC_f9708.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_f9708.dir\\Debug\\cmTC_f9708.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_f9708.dir\\Debug\\cmTC_f9708.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_ALLOCA_H /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_f9708.dir\\Debug\\\\" /Fd"cmTC_f9708.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bi8xo3\\src.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_ALLOCA_H /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_f9708.dir\\Debug\\\\" /Fd"cmTC_f9708.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bi8xo3\\src.c"
          src.c
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bi8xo3\\src.c(2,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渁lloca.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bi8xo3\\cmTC_f9708.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bi8xo3\\cmTC_f9708.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bi8xo3\\cmTC_f9708.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bi8xo3\\src.c(2,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渁lloca.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bi8xo3\\cmTC_f9708.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.76
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckIncludeFile.cmake:90 (try_compile)"
      - "CMakeLists.txt:344 (check_include_file)"
    checks:
      - "Looking for strings.h"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-bfr7vi"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-bfr7vi"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_STRINGS_H"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-bfr7vi'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_ae2c7.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:27:49銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bfr7vi\\cmTC_ae2c7.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_ae2c7.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bfr7vi\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_ae2c7.dir\\Debug\\cmTC_ae2c7.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_ae2c7.dir\\Debug\\cmTC_ae2c7.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_ae2c7.dir\\Debug\\cmTC_ae2c7.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_ae2c7.dir\\Debug\\\\" /Fd"cmTC_ae2c7.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bfr7vi\\CheckIncludeFile.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_ae2c7.dir\\Debug\\\\" /Fd"cmTC_ae2c7.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bfr7vi\\CheckIncludeFile.c"
          CheckIncludeFile.c
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bfr7vi\\CheckIncludeFile.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渟trings.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bfr7vi\\cmTC_ae2c7.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bfr7vi\\cmTC_ae2c7.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bfr7vi\\cmTC_ae2c7.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bfr7vi\\CheckIncludeFile.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渟trings.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bfr7vi\\cmTC_ae2c7.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:04.93
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckIncludeFile.cmake:90 (try_compile)"
      - "CMakeLists.txt:349 (check_include_file)"
    checks:
      - "Looking for sys/select.h"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-uw8svo"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-uw8svo"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_SYS_SELECT_H"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-uw8svo'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_aac32.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:27:56銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uw8svo\\cmTC_aac32.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_aac32.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uw8svo\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_aac32.dir\\Debug\\cmTC_aac32.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_aac32.dir\\Debug\\cmTC_aac32.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_aac32.dir\\Debug\\cmTC_aac32.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_aac32.dir\\Debug\\\\" /Fd"cmTC_aac32.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uw8svo\\CheckIncludeFile.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_aac32.dir\\Debug\\\\" /Fd"cmTC_aac32.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uw8svo\\CheckIncludeFile.c"
          CheckIncludeFile.c
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uw8svo\\CheckIncludeFile.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渟ys/select.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uw8svo\\cmTC_aac32.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uw8svo\\cmTC_aac32.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uw8svo\\cmTC_aac32.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uw8svo\\CheckIncludeFile.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渟ys/select.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uw8svo\\cmTC_aac32.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.38
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckIncludeFile.cmake:90 (try_compile)"
      - "CMakeLists.txt:354 (check_include_file)"
    checks:
      - "Looking for sys/poll.h"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-06rb5c"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-06rb5c"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_SYS_POLL_H"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-06rb5c'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_573f6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:27:58銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-06rb5c\\cmTC_573f6.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_573f6.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-06rb5c\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_573f6.dir\\Debug\\cmTC_573f6.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_573f6.dir\\Debug\\cmTC_573f6.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_573f6.dir\\Debug\\cmTC_573f6.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_573f6.dir\\Debug\\\\" /Fd"cmTC_573f6.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-06rb5c\\CheckIncludeFile.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_573f6.dir\\Debug\\\\" /Fd"cmTC_573f6.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-06rb5c\\CheckIncludeFile.c"
          CheckIncludeFile.c
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-06rb5c\\CheckIncludeFile.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渟ys/poll.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-06rb5c\\cmTC_573f6.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-06rb5c\\cmTC_573f6.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-06rb5c\\cmTC_573f6.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-06rb5c\\CheckIncludeFile.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渟ys/poll.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-06rb5c\\cmTC_573f6.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:02.42
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckIncludeFile.cmake:90 (try_compile)"
      - "CMakeLists.txt:359 (check_include_file)"
    checks:
      - "Looking for poll.h"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-46bzw9"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-46bzw9"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_POLL_H"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-46bzw9'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_55c44.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:28:02銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-46bzw9\\cmTC_55c44.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_55c44.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-46bzw9\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_55c44.dir\\Debug\\cmTC_55c44.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_55c44.dir\\Debug\\cmTC_55c44.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_55c44.dir\\Debug\\cmTC_55c44.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_55c44.dir\\Debug\\\\" /Fd"cmTC_55c44.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-46bzw9\\CheckIncludeFile.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_55c44.dir\\Debug\\\\" /Fd"cmTC_55c44.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-46bzw9\\CheckIncludeFile.c"
          CheckIncludeFile.c
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-46bzw9\\CheckIncludeFile.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減oll.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-46bzw9\\cmTC_55c44.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-46bzw9\\cmTC_55c44.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-46bzw9\\cmTC_55c44.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-46bzw9\\CheckIncludeFile.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減oll.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-46bzw9\\cmTC_55c44.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:04.97
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckIncludeFile.cmake:90 (try_compile)"
      - "CMakeLists.txt:364 (check_include_file)"
    checks:
      - "Looking for inttypes.h"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-z4jbbu"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-z4jbbu"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_INTTYPES_H"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-z4jbbu'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_05efb.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:28:15銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z4jbbu\\cmTC_05efb.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_05efb.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z4jbbu\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_05efb.dir\\Debug\\cmTC_05efb.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_05efb.dir\\Debug\\cmTC_05efb.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_05efb.dir\\Debug\\cmTC_05efb.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_05efb.dir\\Debug\\\\" /Fd"cmTC_05efb.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z4jbbu\\CheckIncludeFile.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_05efb.dir\\Debug\\\\" /Fd"cmTC_05efb.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z4jbbu\\CheckIncludeFile.c"
          CheckIncludeFile.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z4jbbu\\Debug\\cmTC_05efb.exe" /INCREMENTAL /ILK:"cmTC_05efb.dir\\Debug\\cmTC_05efb.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-z4jbbu/Debug/cmTC_05efb.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-z4jbbu/Debug/cmTC_05efb.lib" /MACHINE:X64  /machine:x64 cmTC_05efb.dir\\Debug\\CheckIncludeFile.obj
          cmTC_05efb.vcxproj -> C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z4jbbu\\Debug\\cmTC_05efb.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_05efb.dir\\Debug\\cmTC_05efb.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_05efb.dir\\Debug\\cmTC_05efb.tlog\\cmTC_05efb.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z4jbbu\\cmTC_05efb.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:24.57
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckIncludeFile.cmake:90 (try_compile)"
      - "CMakeLists.txt:369 (check_include_file)"
    checks:
      - "Looking for paths.h"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-hvyi90"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-hvyi90"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_PATHS_H"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-hvyi90'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_23245.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:28:41銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hvyi90\\cmTC_23245.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_23245.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hvyi90\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_23245.dir\\Debug\\cmTC_23245.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_23245.dir\\Debug\\cmTC_23245.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_23245.dir\\Debug\\cmTC_23245.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_23245.dir\\Debug\\\\" /Fd"cmTC_23245.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hvyi90\\CheckIncludeFile.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_23245.dir\\Debug\\\\" /Fd"cmTC_23245.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hvyi90\\CheckIncludeFile.c"
          CheckIncludeFile.c
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hvyi90\\CheckIncludeFile.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減aths.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hvyi90\\cmTC_23245.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hvyi90\\cmTC_23245.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hvyi90\\cmTC_23245.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hvyi90\\CheckIncludeFile.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減aths.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hvyi90\\cmTC_23245.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:03.45
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "CMakeLists.txt:386 (check_function_exists)"
    checks:
      - "Looking for strcasestr"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-ynjju8"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-ynjju8"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_STRCASESTR"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-ynjju8'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_7276e.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:28:47銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ynjju8\\cmTC_7276e.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_7276e.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ynjju8\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_7276e.dir\\Debug\\cmTC_7276e.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_7276e.dir\\Debug\\cmTC_7276e.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_7276e.dir\\Debug\\cmTC_7276e.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=strcasestr /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7276e.dir\\Debug\\\\" /Fd"cmTC_7276e.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ynjju8\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=strcasestr /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7276e.dir\\Debug\\\\" /Fd"cmTC_7276e.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ynjju8\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ynjju8\\Debug\\cmTC_7276e.exe" /INCREMENTAL /ILK:"cmTC_7276e.dir\\Debug\\cmTC_7276e.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-ynjju8/Debug/cmTC_7276e.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-ynjju8/Debug/cmTC_7276e.lib" /MACHINE:X64  /machine:x64 cmTC_7276e.dir\\Debug\\CheckFunctionExists.obj
        CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?strcasestr锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ynjju8\\cmTC_7276e.vcxproj]
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ynjju8\\Debug\\cmTC_7276e.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ynjju8\\cmTC_7276e.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ynjju8\\cmTC_7276e.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ynjju8\\cmTC_7276e.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?strcasestr锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ynjju8\\cmTC_7276e.vcxproj]
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ynjju8\\Debug\\cmTC_7276e.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ynjju8\\cmTC_7276e.vcxproj]
        
            0 涓鍛?
            2 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:21.60
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "CMakeLists.txt:386 (check_function_exists)"
    checks:
      - "Looking for memmem"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-h2blog"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-h2blog"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_MEMMEM"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-h2blog'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_28ab3.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:29:10銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h2blog\\cmTC_28ab3.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_28ab3.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h2blog\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_28ab3.dir\\Debug\\cmTC_28ab3.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_28ab3.dir\\Debug\\cmTC_28ab3.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_28ab3.dir\\Debug\\cmTC_28ab3.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=memmem /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_28ab3.dir\\Debug\\\\" /Fd"cmTC_28ab3.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h2blog\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=memmem /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_28ab3.dir\\Debug\\\\" /Fd"cmTC_28ab3.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h2blog\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h2blog\\Debug\\cmTC_28ab3.exe" /INCREMENTAL /ILK:"cmTC_28ab3.dir\\Debug\\cmTC_28ab3.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-h2blog/Debug/cmTC_28ab3.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-h2blog/Debug/cmTC_28ab3.lib" /MACHINE:X64  /machine:x64 cmTC_28ab3.dir\\Debug\\CheckFunctionExists.obj
        CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?memmem锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h2blog\\cmTC_28ab3.vcxproj]
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h2blog\\Debug\\cmTC_28ab3.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h2blog\\cmTC_28ab3.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h2blog\\cmTC_28ab3.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h2blog\\cmTC_28ab3.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?memmem锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h2blog\\cmTC_28ab3.vcxproj]
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h2blog\\Debug\\cmTC_28ab3.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-h2blog\\cmTC_28ab3.vcxproj]
        
            0 涓鍛?
            2 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:21.18
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "CMakeLists.txt:386 (check_function_exists)"
    checks:
      - "Looking for strlcpy"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-slkib0"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-slkib0"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_STRLCPY"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-slkib0'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_c056f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:29:33銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-slkib0\\cmTC_c056f.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_c056f.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-slkib0\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_c056f.dir\\Debug\\cmTC_c056f.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_c056f.dir\\Debug\\cmTC_c056f.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_c056f.dir\\Debug\\cmTC_c056f.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=strlcpy /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_c056f.dir\\Debug\\\\" /Fd"cmTC_c056f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-slkib0\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=strlcpy /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_c056f.dir\\Debug\\\\" /Fd"cmTC_c056f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-slkib0\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-slkib0\\Debug\\cmTC_c056f.exe" /INCREMENTAL /ILK:"cmTC_c056f.dir\\Debug\\cmTC_c056f.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-slkib0/Debug/cmTC_c056f.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-slkib0/Debug/cmTC_c056f.lib" /MACHINE:X64  /machine:x64 cmTC_c056f.dir\\Debug\\CheckFunctionExists.obj
        CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?strlcpy锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-slkib0\\cmTC_c056f.vcxproj]
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-slkib0\\Debug\\cmTC_c056f.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-slkib0\\cmTC_c056f.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-slkib0\\cmTC_c056f.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-slkib0\\cmTC_c056f.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?strlcpy锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-slkib0\\cmTC_c056f.vcxproj]
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-slkib0\\Debug\\cmTC_c056f.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-slkib0\\cmTC_c056f.vcxproj]
        
            0 涓鍛?
            2 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:13.10
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "CMakeLists.txt:386 (check_function_exists)"
    checks:
      - "Looking for strtoimax"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-l8gq3o"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-l8gq3o"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_STRTOIMAX"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-l8gq3o'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_f9fef.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:29:47銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l8gq3o\\cmTC_f9fef.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_f9fef.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l8gq3o\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_f9fef.dir\\Debug\\cmTC_f9fef.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_f9fef.dir\\Debug\\cmTC_f9fef.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_f9fef.dir\\Debug\\cmTC_f9fef.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=strtoimax /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_f9fef.dir\\Debug\\\\" /Fd"cmTC_f9fef.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l8gq3o\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=strtoimax /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_f9fef.dir\\Debug\\\\" /Fd"cmTC_f9fef.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l8gq3o\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l8gq3o\\Debug\\cmTC_f9fef.exe" /INCREMENTAL /ILK:"cmTC_f9fef.dir\\Debug\\cmTC_f9fef.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-l8gq3o/Debug/cmTC_f9fef.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-l8gq3o/Debug/cmTC_f9fef.lib" /MACHINE:X64  /machine:x64 cmTC_f9fef.dir\\Debug\\CheckFunctionExists.obj
          cmTC_f9fef.vcxproj -> C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l8gq3o\\Debug\\cmTC_f9fef.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_f9fef.dir\\Debug\\cmTC_f9fef.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_f9fef.dir\\Debug\\cmTC_f9fef.tlog\\cmTC_f9fef.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l8gq3o\\cmTC_f9fef.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:18.03
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "CMakeLists.txt:386 (check_function_exists)"
    checks:
      - "Looking for strtoumax"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-nmsmw0"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-nmsmw0"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_STRTOUMAX"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-nmsmw0'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_652e4.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:30:08銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nmsmw0\\cmTC_652e4.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_652e4.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nmsmw0\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_652e4.dir\\Debug\\cmTC_652e4.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_652e4.dir\\Debug\\cmTC_652e4.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_652e4.dir\\Debug\\cmTC_652e4.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=strtoumax /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_652e4.dir\\Debug\\\\" /Fd"cmTC_652e4.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nmsmw0\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=strtoumax /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_652e4.dir\\Debug\\\\" /Fd"cmTC_652e4.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nmsmw0\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nmsmw0\\Debug\\cmTC_652e4.exe" /INCREMENTAL /ILK:"cmTC_652e4.dir\\Debug\\cmTC_652e4.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-nmsmw0/Debug/cmTC_652e4.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-nmsmw0/Debug/cmTC_652e4.lib" /MACHINE:X64  /machine:x64 cmTC_652e4.dir\\Debug\\CheckFunctionExists.obj
          cmTC_652e4.vcxproj -> C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nmsmw0\\Debug\\cmTC_652e4.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_652e4.dir\\Debug\\cmTC_652e4.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_652e4.dir\\Debug\\cmTC_652e4.tlog\\cmTC_652e4.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nmsmw0\\cmTC_652e4.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:14.24
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "CMakeLists.txt:386 (check_function_exists)"
    checks:
      - "Looking for strtoull"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-oazvuh"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-oazvuh"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_STRTOULL"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-oazvuh'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_83d11.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:30:23銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oazvuh\\cmTC_83d11.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_83d11.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oazvuh\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_83d11.dir\\Debug\\cmTC_83d11.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_83d11.dir\\Debug\\cmTC_83d11.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_83d11.dir\\Debug\\cmTC_83d11.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=strtoull /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_83d11.dir\\Debug\\\\" /Fd"cmTC_83d11.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oazvuh\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=strtoull /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_83d11.dir\\Debug\\\\" /Fd"cmTC_83d11.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oazvuh\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oazvuh\\Debug\\cmTC_83d11.exe" /INCREMENTAL /ILK:"cmTC_83d11.dir\\Debug\\cmTC_83d11.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-oazvuh/Debug/cmTC_83d11.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-oazvuh/Debug/cmTC_83d11.lib" /MACHINE:X64  /machine:x64 cmTC_83d11.dir\\Debug\\CheckFunctionExists.obj
          cmTC_83d11.vcxproj -> C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oazvuh\\Debug\\cmTC_83d11.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_83d11.dir\\Debug\\cmTC_83d11.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_83d11.dir\\Debug\\cmTC_83d11.tlog\\cmTC_83d11.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oazvuh\\cmTC_83d11.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.63
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "CMakeLists.txt:386 (check_function_exists)"
    checks:
      - "Looking for setenv"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-dak96y"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-dak96y"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_SETENV"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-dak96y'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_d0dc4.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:30:25銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dak96y\\cmTC_d0dc4.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_d0dc4.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dak96y\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_d0dc4.dir\\Debug\\cmTC_d0dc4.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_d0dc4.dir\\Debug\\cmTC_d0dc4.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_d0dc4.dir\\Debug\\cmTC_d0dc4.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=setenv /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_d0dc4.dir\\Debug\\\\" /Fd"cmTC_d0dc4.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dak96y\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=setenv /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_d0dc4.dir\\Debug\\\\" /Fd"cmTC_d0dc4.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dak96y\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dak96y\\Debug\\cmTC_d0dc4.exe" /INCREMENTAL /ILK:"cmTC_d0dc4.dir\\Debug\\cmTC_d0dc4.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-dak96y/Debug/cmTC_d0dc4.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-dak96y/Debug/cmTC_d0dc4.lib" /MACHINE:X64  /machine:x64 cmTC_d0dc4.dir\\Debug\\CheckFunctionExists.obj
        CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?setenv锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dak96y\\cmTC_d0dc4.vcxproj]
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dak96y\\Debug\\cmTC_d0dc4.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dak96y\\cmTC_d0dc4.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dak96y\\cmTC_d0dc4.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dak96y\\cmTC_d0dc4.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?setenv锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dak96y\\cmTC_d0dc4.vcxproj]
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dak96y\\Debug\\cmTC_d0dc4.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dak96y\\cmTC_d0dc4.vcxproj]
        
            0 涓鍛?
            2 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:03.64
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "CMakeLists.txt:386 (check_function_exists)"
    checks:
      - "Looking for mkdtemp"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-rzf9nd"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-rzf9nd"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_MKDTEMP"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-rzf9nd'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_14b25.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:30:30銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rzf9nd\\cmTC_14b25.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_14b25.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rzf9nd\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_14b25.dir\\Debug\\cmTC_14b25.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_14b25.dir\\Debug\\cmTC_14b25.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_14b25.dir\\Debug\\cmTC_14b25.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=mkdtemp /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_14b25.dir\\Debug\\\\" /Fd"cmTC_14b25.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rzf9nd\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=mkdtemp /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_14b25.dir\\Debug\\\\" /Fd"cmTC_14b25.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rzf9nd\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rzf9nd\\Debug\\cmTC_14b25.exe" /INCREMENTAL /ILK:"cmTC_14b25.dir\\Debug\\cmTC_14b25.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-rzf9nd/Debug/cmTC_14b25.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-rzf9nd/Debug/cmTC_14b25.lib" /MACHINE:X64  /machine:x64 cmTC_14b25.dir\\Debug\\CheckFunctionExists.obj
        CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?mkdtemp锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rzf9nd\\cmTC_14b25.vcxproj]
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rzf9nd\\Debug\\cmTC_14b25.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rzf9nd\\cmTC_14b25.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rzf9nd\\cmTC_14b25.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rzf9nd\\cmTC_14b25.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?mkdtemp锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rzf9nd\\cmTC_14b25.vcxproj]
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rzf9nd\\Debug\\cmTC_14b25.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rzf9nd\\cmTC_14b25.vcxproj]
        
            0 涓鍛?
            2 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:16.09
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "CMakeLists.txt:386 (check_function_exists)"
    checks:
      - "Looking for poll"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-kutv93"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-kutv93"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_POLL"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-kutv93'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_e0fc5.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:30:47銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kutv93\\cmTC_e0fc5.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_e0fc5.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kutv93\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_e0fc5.dir\\Debug\\cmTC_e0fc5.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_e0fc5.dir\\Debug\\cmTC_e0fc5.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_e0fc5.dir\\Debug\\cmTC_e0fc5.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=poll /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e0fc5.dir\\Debug\\\\" /Fd"cmTC_e0fc5.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kutv93\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=poll /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e0fc5.dir\\Debug\\\\" /Fd"cmTC_e0fc5.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kutv93\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kutv93\\Debug\\cmTC_e0fc5.exe" /INCREMENTAL /ILK:"cmTC_e0fc5.dir\\Debug\\cmTC_e0fc5.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-kutv93/Debug/cmTC_e0fc5.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-kutv93/Debug/cmTC_e0fc5.lib" /MACHINE:X64  /machine:x64 cmTC_e0fc5.dir\\Debug\\CheckFunctionExists.obj
        CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?poll锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kutv93\\cmTC_e0fc5.vcxproj]
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kutv93\\Debug\\cmTC_e0fc5.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kutv93\\cmTC_e0fc5.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kutv93\\cmTC_e0fc5.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kutv93\\cmTC_e0fc5.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?poll锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kutv93\\cmTC_e0fc5.vcxproj]
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kutv93\\Debug\\cmTC_e0fc5.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kutv93\\cmTC_e0fc5.vcxproj]
        
            0 涓鍛?
            2 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:13.36
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "CMakeLists.txt:386 (check_function_exists)"
    checks:
      - "Looking for pread"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-f7j5zp"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-f7j5zp"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_PREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-f7j5zp'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_2df3f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:31:02銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-f7j5zp\\cmTC_2df3f.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_2df3f.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-f7j5zp\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_2df3f.dir\\Debug\\cmTC_2df3f.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_2df3f.dir\\Debug\\cmTC_2df3f.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_2df3f.dir\\Debug\\cmTC_2df3f.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pread /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_2df3f.dir\\Debug\\\\" /Fd"cmTC_2df3f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-f7j5zp\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pread /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_2df3f.dir\\Debug\\\\" /Fd"cmTC_2df3f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-f7j5zp\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-f7j5zp\\Debug\\cmTC_2df3f.exe" /INCREMENTAL /ILK:"cmTC_2df3f.dir\\Debug\\cmTC_2df3f.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-f7j5zp/Debug/cmTC_2df3f.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-f7j5zp/Debug/cmTC_2df3f.lib" /MACHINE:X64  /machine:x64 cmTC_2df3f.dir\\Debug\\CheckFunctionExists.obj
        CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?pread锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-f7j5zp\\cmTC_2df3f.vcxproj]
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-f7j5zp\\Debug\\cmTC_2df3f.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-f7j5zp\\cmTC_2df3f.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-f7j5zp\\cmTC_2df3f.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-f7j5zp\\cmTC_2df3f.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?pread锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-f7j5zp\\cmTC_2df3f.vcxproj]
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-f7j5zp\\Debug\\cmTC_2df3f.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-f7j5zp\\cmTC_2df3f.vcxproj]
        
            0 涓鍛?
            2 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:26.32
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "CMakeLists.txt:436 (check_function_exists)"
    checks:
      - "Looking for getdelim"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-knj73k"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-knj73k"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_GETDELIM"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-knj73k'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_a105f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:31:30銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-knj73k\\cmTC_a105f.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_a105f.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-knj73k\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_a105f.dir\\Debug\\cmTC_a105f.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_a105f.dir\\Debug\\cmTC_a105f.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_a105f.dir\\Debug\\cmTC_a105f.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=getdelim /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_a105f.dir\\Debug\\\\" /Fd"cmTC_a105f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-knj73k\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=getdelim /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_a105f.dir\\Debug\\\\" /Fd"cmTC_a105f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-knj73k\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-knj73k\\Debug\\cmTC_a105f.exe" /INCREMENTAL /ILK:"cmTC_a105f.dir\\Debug\\cmTC_a105f.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-knj73k/Debug/cmTC_a105f.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-knj73k/Debug/cmTC_a105f.lib" /MACHINE:X64  /machine:x64 cmTC_a105f.dir\\Debug\\CheckFunctionExists.obj
        CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?getdelim锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-knj73k\\cmTC_a105f.vcxproj]
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-knj73k\\Debug\\cmTC_a105f.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-knj73k\\cmTC_a105f.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-knj73k\\cmTC_a105f.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-knj73k\\cmTC_a105f.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?getdelim锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-knj73k\\cmTC_a105f.vcxproj]
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-knj73k\\Debug\\cmTC_a105f.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-knj73k\\cmTC_a105f.vcxproj]
        
            0 涓鍛?
            2 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:21.57
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckFunctionExists.cmake:86 (try_compile)"
      - "CMakeLists.txt:441 (check_function_exists)"
    checks:
      - "Looking for clock_gettime"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-3feosz"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-3feosz"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_CLOCK_GETTIME"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-3feosz'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_075b5.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:31:54銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3feosz\\cmTC_075b5.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_075b5.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3feosz\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_075b5.dir\\Debug\\cmTC_075b5.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_075b5.dir\\Debug\\cmTC_075b5.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_075b5.dir\\Debug\\cmTC_075b5.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=clock_gettime /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_075b5.dir\\Debug\\\\" /Fd"cmTC_075b5.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3feosz\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=clock_gettime /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_075b5.dir\\Debug\\\\" /Fd"cmTC_075b5.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3feosz\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3feosz\\Debug\\cmTC_075b5.exe" /INCREMENTAL /ILK:"cmTC_075b5.dir\\Debug\\cmTC_075b5.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-3feosz/Debug/cmTC_075b5.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-3feosz/Debug/cmTC_075b5.lib" /MACHINE:X64  /machine:x64 cmTC_075b5.dir\\Debug\\CheckFunctionExists.obj
        CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?clock_gettime锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3feosz\\cmTC_075b5.vcxproj]
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3feosz\\Debug\\cmTC_075b5.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3feosz\\cmTC_075b5.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3feosz\\cmTC_075b5.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3feosz\\cmTC_075b5.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?clock_gettime锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3feosz\\cmTC_075b5.vcxproj]
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3feosz\\Debug\\cmTC_075b5.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3feosz\\cmTC_075b5.vcxproj]
        
            0 涓鍛?
            2 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:23.74
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckSymbolExists.cmake:154 (try_compile)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckSymbolExists.cmake:66 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "CMakeLists.txt:442 (check_symbol_exists)"
    checks:
      - "Looking for CLOCK_MONOTONIC"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-n3c0b0"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-n3c0b0"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_CLOCK_MONOTONIC"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-n3c0b0'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_28589.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:32:19銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-n3c0b0\\cmTC_28589.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_28589.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-n3c0b0\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_28589.dir\\Debug\\cmTC_28589.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_28589.dir\\Debug\\cmTC_28589.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_28589.dir\\Debug\\cmTC_28589.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_28589.dir\\Debug\\\\" /Fd"cmTC_28589.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-n3c0b0\\CheckSymbolExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_28589.dir\\Debug\\\\" /Fd"cmTC_28589.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-n3c0b0\\CheckSymbolExists.c"
          CheckSymbolExists.c
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-n3c0b0\\CheckSymbolExists.c(8,19): error C2065: 鈥淐LOCK_MONOTONIC鈥? 鏈０鏄庣殑鏍囪瘑绗?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-n3c0b0\\cmTC_28589.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-n3c0b0\\cmTC_28589.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-n3c0b0\\cmTC_28589.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-n3c0b0\\CheckSymbolExists.c(8,19): error C2065: 鈥淐LOCK_MONOTONIC鈥? 鏈０鏄庣殑鏍囪瘑绗?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-n3c0b0\\cmTC_28589.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.76
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckSourceCompiles.cmake:77 (cmake_check_source_compiles)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckStructHasMember.cmake:77 (CHECK_SOURCE_COMPILES)"
      - "CMakeLists.txt:451 (check_struct_has_member)"
    checks:
      - "Performing Test STRUCT_STAT_HAS_ST_BLOCKS"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-wxe0gt"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-wxe0gt"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "STRUCT_STAT_HAS_ST_BLOCKS"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-wxe0gt'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_bc789.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:32:21銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wxe0gt\\cmTC_bc789.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_bc789.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wxe0gt\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_bc789.dir\\Debug\\cmTC_bc789.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_bc789.dir\\Debug\\cmTC_bc789.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_bc789.dir\\Debug\\cmTC_bc789.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D STRUCT_STAT_HAS_ST_BLOCKS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_bc789.dir\\Debug\\\\" /Fd"cmTC_bc789.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wxe0gt\\src.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D STRUCT_STAT_HAS_ST_BLOCKS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_bc789.dir\\Debug\\\\" /Fd"cmTC_bc789.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wxe0gt\\src.c"
          src.c
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wxe0gt\\src.c(6,36): error C2039: "st_blocks": 涓嶆槸 "stat" 鐨勬垚鍛?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wxe0gt\\cmTC_bc789.vcxproj]
              C:\\Program Files (x86)\\Windows Kits\\10\\Include\\10.0.26100.0\\ucrt\\sys\\stat.h(87,12):
              鍙傝鈥渟tat鈥濈殑澹版槑
          
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wxe0gt\\cmTC_bc789.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wxe0gt\\cmTC_bc789.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wxe0gt\\src.c(6,36): error C2039: "st_blocks": 涓嶆槸 "stat" 鐨勬垚鍛?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wxe0gt\\cmTC_bc789.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.09
        
      exitCode: 1
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/Internal/CheckSourceRuns.cmake:93 (try_run)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "CMakeLists.txt:457 (check_c_source_runs)"
    checks:
      - "Performing Test SNPRINTF_OK"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-ywo8c7"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-ywo8c7"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "SNPRINTF_OK_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-ywo8c7'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_89cc1.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:32:24銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ywo8c7\\cmTC_89cc1.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_89cc1.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ywo8c7\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_89cc1.dir\\Debug\\cmTC_89cc1.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_89cc1.dir\\Debug\\cmTC_89cc1.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_89cc1.dir\\Debug\\cmTC_89cc1.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D SNPRINTF_OK /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_89cc1.dir\\Debug\\\\" /Fd"cmTC_89cc1.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ywo8c7\\src.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D SNPRINTF_OK /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_89cc1.dir\\Debug\\\\" /Fd"cmTC_89cc1.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ywo8c7\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ywo8c7\\Debug\\cmTC_89cc1.exe" /INCREMENTAL /ILK:"cmTC_89cc1.dir\\Debug\\cmTC_89cc1.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-ywo8c7/Debug/cmTC_89cc1.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-ywo8c7/Debug/cmTC_89cc1.lib" /MACHINE:X64  /machine:x64 cmTC_89cc1.dir\\Debug\\src.obj
          cmTC_89cc1.vcxproj -> C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ywo8c7\\Debug\\cmTC_89cc1.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_89cc1.dir\\Debug\\cmTC_89cc1.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_89cc1.dir\\Debug\\cmTC_89cc1.tlog\\cmTC_89cc1.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ywo8c7\\cmTC_89cc1.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:16.35
        
      exitCode: 0
    runResult:
      variable: "SNPRINTF_OK_EXITCODE"
      cached: true
      stdout: |
      stderr: |
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/Internal/CheckSourceRuns.cmake:93 (try_run)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "CMakeLists.txt:492 (check_c_source_runs)"
    checks:
      - "Performing Test FREAD_READS_DIRECTORIES_NO"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-wfytjo"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-wfytjo"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "FREAD_READS_DIRECTORIES_NO_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-wfytjo'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_4fee1.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:32:44銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wfytjo\\cmTC_4fee1.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_4fee1.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wfytjo\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_4fee1.dir\\Debug\\cmTC_4fee1.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_4fee1.dir\\Debug\\cmTC_4fee1.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_4fee1.dir\\Debug\\cmTC_4fee1.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D FREAD_READS_DIRECTORIES_NO /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_4fee1.dir\\Debug\\\\" /Fd"cmTC_4fee1.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wfytjo\\src.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D FREAD_READS_DIRECTORIES_NO /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_4fee1.dir\\Debug\\\\" /Fd"cmTC_4fee1.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wfytjo\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wfytjo\\Debug\\cmTC_4fee1.exe" /INCREMENTAL /ILK:"cmTC_4fee1.dir\\Debug\\cmTC_4fee1.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-wfytjo/Debug/cmTC_4fee1.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-wfytjo/Debug/cmTC_4fee1.lib" /MACHINE:X64  /machine:x64 cmTC_4fee1.dir\\Debug\\src.obj
          cmTC_4fee1.vcxproj -> C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wfytjo\\Debug\\cmTC_4fee1.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_4fee1.dir\\Debug\\cmTC_4fee1.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_4fee1.dir\\Debug\\cmTC_4fee1.tlog\\cmTC_4fee1.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-wfytjo\\cmTC_4fee1.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:14.72
        
      exitCode: 0
    runResult:
      variable: "FREAD_READS_DIRECTORIES_NO_EXITCODE"
      cached: true
      stdout: |
      stderr: |
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "CMakeLists.txt:507 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_REGEX"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-dez84k"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-dez84k"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_REGEX"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-dez84k'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_2032a.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:33:01銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dez84k\\cmTC_2032a.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_2032a.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dez84k\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_2032a.dir\\Debug\\cmTC_2032a.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_2032a.dir\\Debug\\cmTC_2032a.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_2032a.dir\\Debug\\cmTC_2032a.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_REGEX /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_2032a.dir\\Debug\\\\" /Fd"cmTC_2032a.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dez84k\\src.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_REGEX /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_2032a.dir\\Debug\\\\" /Fd"cmTC_2032a.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dez84k\\src.c"
          src.c
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dez84k\\src.c(2,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渞egex.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dez84k\\cmTC_2032a.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dez84k\\cmTC_2032a.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dez84k\\cmTC_2032a.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dez84k\\src.c(2,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渞egex.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-dez84k\\cmTC_2032a.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:02.25
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "CMakeLists.txt:525 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_BSD_SYSCTL"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-p9dpll"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-p9dpll"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_BSD_SYSCTL"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-p9dpll'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_88c95.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:33:04銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9dpll\\cmTC_88c95.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_88c95.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9dpll\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_88c95.dir\\Debug\\cmTC_88c95.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_88c95.dir\\Debug\\cmTC_88c95.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_88c95.dir\\Debug\\cmTC_88c95.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_BSD_SYSCTL /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_88c95.dir\\Debug\\\\" /Fd"cmTC_88c95.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9dpll\\src.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_BSD_SYSCTL /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_88c95.dir\\Debug\\\\" /Fd"cmTC_88c95.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9dpll\\src.c"
          src.c
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9dpll\\src.c(4,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渟ys/sysctl.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9dpll\\cmTC_88c95.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9dpll\\cmTC_88c95.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9dpll\\cmTC_88c95.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9dpll\\src.c(4,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渟ys/sysctl.h鈥? No such file or directory [C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9dpll\\cmTC_88c95.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.83
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "CMakeLists.txt:548 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_NEW_ICONV"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-7svr49"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-7svr49"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "HAVE_NEW_ICONV"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-7svr49'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_93338.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:33:05銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7svr49\\cmTC_93338.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_93338.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7svr49\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_93338.dir\\Debug\\cmTC_93338.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_93338.dir\\Debug\\cmTC_93338.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_93338.dir\\Debug\\cmTC_93338.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\compat\\vcbuild\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_NEW_ICONV /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_93338.dir\\Debug\\\\" /Fd"cmTC_93338.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7svr49\\src.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\compat\\vcbuild\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_NEW_ICONV /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_93338.dir\\Debug\\\\" /Fd"cmTC_93338.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7svr49\\src.c"
          src.c
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\compat\\vcbuild\\vcpkg\\installed\\x64-windows\\include\\iconv.h(1,1): warning C4819: 璇ユ枃浠跺寘鍚笉鑳藉湪褰撳墠浠ｇ爜椤?936)涓〃绀虹殑瀛楃銆傝灏嗚鏂囦欢淇濆瓨涓?Unicode 鏍煎紡浠ラ槻姝㈡暟鎹涪澶?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7svr49\\cmTC_93338.vcxproj]
          (缂栬瘧婧愭枃浠垛€渟rc.c鈥?
          
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7svr49\\Debug\\cmTC_93338.exe" /INCREMENTAL /ILK:"cmTC_93338.dir\\Debug\\cmTC_93338.ilk" /NOLOGO "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\compat\\vcbuild\\vcpkg\\installed\\x64-windows\\debug\\lib\\iconv.lib" "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\compat\\vcbuild\\vcpkg\\installed\\x64-windows\\debug\\lib\\charset.lib" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-7svr49/Debug/cmTC_93338.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-7svr49/Debug/cmTC_93338.lib" /MACHINE:X64  /machine:x64 cmTC_93338.dir\\Debug\\src.obj
          cmTC_93338.vcxproj -> C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7svr49\\Debug\\cmTC_93338.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_93338.dir\\Debug\\cmTC_93338.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_93338.dir\\Debug\\cmTC_93338.tlog\\cmTC_93338.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7svr49\\cmTC_93338.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7svr49\\cmTC_93338.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\compat\\vcbuild\\vcpkg\\installed\\x64-windows\\include\\iconv.h(1,1): warning C4819: 璇ユ枃浠跺寘鍚笉鑳藉湪褰撳墠浠ｇ爜椤?936)涓〃绀虹殑瀛楃銆傝灏嗚鏂囦欢淇濆瓨涓?Unicode 鏍煎紡浠ラ槻姝㈡暟鎹涪澶?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7svr49\\cmTC_93338.vcxproj]
        
            1 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:04.05
        
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/Internal/CheckSourceRuns.cmake:93 (try_run)"
      - "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "CMakeLists.txt:566 (check_c_source_runs)"
    checks:
      - "Performing Test ICONV_DOESNOT_OMIT_BOM"
    directories:
      source: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-1on2zo"
      binary: "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-1on2zo"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg"
    buildResult:
      variable: "ICONV_DOESNOT_OMIT_BOM_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-1on2zo'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_86042.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.10+8b8e13593
        鐢熸垚鍚姩鏃堕棿涓?13/6/2025 涓嬪崍10:33:11銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1on2zo\\cmTC_86042.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_86042.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1on2zo\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_86042.dir\\Debug\\cmTC_86042.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_86042.dir\\Debug\\cmTC_86042.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_86042.dir\\Debug\\cmTC_86042.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\compat\\vcbuild\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D ICONV_DOESNOT_OMIT_BOM /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_86042.dir\\Debug\\\\" /Fd"cmTC_86042.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1on2zo\\src.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35209 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\compat\\vcbuild\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D ICONV_DOESNOT_OMIT_BOM /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_86042.dir\\Debug\\\\" /Fd"cmTC_86042.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1on2zo\\src.c"
          src.c
        C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\compat\\vcbuild\\vcpkg\\installed\\x64-windows\\include\\iconv.h(1,1): warning C4819: 璇ユ枃浠跺寘鍚笉鑳藉湪褰撳墠浠ｇ爜椤?936)涓〃绀虹殑瀛楃銆傝灏嗚鏂囦欢淇濆瓨涓?Unicode 鏍煎紡浠ラ槻姝㈡暟鎹涪澶?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1on2zo\\cmTC_86042.vcxproj]
          (缂栬瘧婧愭枃浠垛€渟rc.c鈥?
          
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1on2zo\\Debug\\cmTC_86042.exe" /INCREMENTAL /ILK:"cmTC_86042.dir\\Debug\\cmTC_86042.ilk" /NOLOGO "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\compat\\vcbuild\\vcpkg\\installed\\x64-windows\\debug\\lib\\iconv.lib" "C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\compat\\vcbuild\\vcpkg\\installed\\x64-windows\\debug\\lib\\charset.lib" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-1on2zo/Debug/cmTC_86042.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/CMakeScratch/TryCompile-1on2zo/Debug/cmTC_86042.lib" /MACHINE:X64  /machine:x64 cmTC_86042.dir\\Debug\\src.obj
          cmTC_86042.vcxproj -> C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1on2zo\\Debug\\cmTC_86042.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_86042.dir\\Debug\\cmTC_86042.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_86042.dir\\Debug\\cmTC_86042.tlog\\cmTC_86042.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1on2zo\\cmTC_86042.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
        
        鈥淐:\\Users\\potat\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1on2zo\\cmTC_86042.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\compat\\vcbuild\\vcpkg\\installed\\x64-windows\\include\\iconv.h(1,1): warning C4819: 璇ユ枃浠跺寘鍚笉鑳藉湪褰撳墠浠ｇ爜椤?936)涓〃绀虹殑瀛楃銆傝灏嗚鏂囦欢淇濆瓨涓?Unicode 鏍煎紡浠ラ槻姝㈡暟鎹涪澶?[C:\\Users\\<USER>\\Desktop\\workspace\\tools\\git\\git\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1on2zo\\cmTC_86042.vcxproj]
        
            1 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.84
        
      exitCode: 0
    runResult:
      variable: "ICONV_DOESNOT_OMIT_BOM_EXITCODE"
      cached: true
      stdout: |
      stderr: |
        Exit code 0xc0000135
      exitCode: "FAILED_TO_RUN"
...
