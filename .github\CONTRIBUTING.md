## Contributing to Git

Thanks for taking the time to contribute to Git! Please be advised that the
Git community does not use github.com for their contributions. Instead, we use
a mailing list (*******************) for code submissions, code
reviews, and bug reports.

Nevertheless, you can use [GitGitGadget](https://gitgitgadget.github.io/) to
conveniently send your Pull Requests commits to our mailing list.

Please read ["A note from the maintainer"](https://git.kernel.org/pub/scm/git/git.git/plain/MaintNotes?h=todo)
to learn how the Git project is managed, and how you can work with it.
In addition, we highly recommend you to read [our submission guidelines](../Documentation/SubmittingPatches).

If you prefer video, then [this talk](https://www.youtube.com/watch?v=Q7i_qQW__q4&feature=youtu.be&t=6m4s)
might be useful to you as the presenter walks you through the contribution
process by example.

Or, you can follow the ["My First Contribution"](https://git-scm.com/docs/MyFirstContribution)
tutorial for another example of the contribution process.

Your friendly Git community!
