file(CREATE_LINK git.exe git-add.exe)
file(CREATE_LINK git.exe git-am.exe)
file(CREATE_LINK git.exe git-annotate.exe)
file(CREATE_LINK git.exe git-apply.exe)
file(CREATE_LINK git.exe git-archive.exe)
file(CREATE_LINK git.exe git-backfill.exe)
file(CREATE_LINK git.exe git-bisect.exe)
file(CREATE_LINK git.exe git-blame.exe)
file(CREATE_LINK git.exe git-branch.exe)
file(CREATE_LINK git.exe git-bugreport.exe)
file(CREATE_LINK git.exe git-bundle.exe)
file(CREATE_LINK git.exe git-cat-file.exe)
file(CREATE_LINK git.exe git-check-attr.exe)
file(CREATE_LINK git.exe git-check-ignore.exe)
file(CREATE_LINK git.exe git-check-mailmap.exe)
file(CREATE_LINK git.exe git-check-ref-format.exe)
file(CREATE_LINK git.exe git-checkout--worker.exe)
file(CREATE_LINK git.exe git-checkout-index.exe)
file(CREATE_LINK git.exe git-checkout.exe)
file(CREATE_LINK git.exe git-clean.exe)
file(CREATE_LINK git.exe git-clone.exe)
file(CREATE_LINK git.exe git-column.exe)
file(CREATE_LINK git.exe git-commit-graph.exe)
file(CREATE_LINK git.exe git-commit-tree.exe)
file(CREATE_LINK git.exe git-commit.exe)
file(CREATE_LINK git.exe git-config.exe)
file(CREATE_LINK git.exe git-count-objects.exe)
file(CREATE_LINK git.exe git-credential-cache--daemon.exe)
file(CREATE_LINK git.exe git-credential-cache.exe)
file(CREATE_LINK git.exe git-credential-store.exe)
file(CREATE_LINK git.exe git-credential.exe)
file(CREATE_LINK git.exe git-describe.exe)
file(CREATE_LINK git.exe git-diagnose.exe)
file(CREATE_LINK git.exe git-diff-files.exe)
file(CREATE_LINK git.exe git-diff-index.exe)
file(CREATE_LINK git.exe git-diff-pairs.exe)
file(CREATE_LINK git.exe git-diff-tree.exe)
file(CREATE_LINK git.exe git-diff.exe)
file(CREATE_LINK git.exe git-difftool.exe)
file(CREATE_LINK git.exe git-fast-export.exe)
file(CREATE_LINK git.exe git-fast-import.exe)
file(CREATE_LINK git.exe git-fetch-pack.exe)
file(CREATE_LINK git.exe git-fetch.exe)
file(CREATE_LINK git.exe git-fmt-merge-msg.exe)
file(CREATE_LINK git.exe git-for-each-ref.exe)
file(CREATE_LINK git.exe git-for-each-repo.exe)
file(CREATE_LINK git.exe git-fsck.exe)
file(CREATE_LINK git.exe git-fsmonitor--daemon.exe)
file(CREATE_LINK git.exe git-gc.exe)
file(CREATE_LINK git.exe git-get-tar-commit-id.exe)
file(CREATE_LINK git.exe git-grep.exe)
file(CREATE_LINK git.exe git-hash-object.exe)
file(CREATE_LINK git.exe git-help.exe)
file(CREATE_LINK git.exe git-hook.exe)
file(CREATE_LINK git.exe git-index-pack.exe)
file(CREATE_LINK git.exe git-init-db.exe)
file(CREATE_LINK git.exe git-interpret-trailers.exe)
file(CREATE_LINK git.exe git-log.exe)
file(CREATE_LINK git.exe git-ls-files.exe)
file(CREATE_LINK git.exe git-ls-remote.exe)
file(CREATE_LINK git.exe git-ls-tree.exe)
file(CREATE_LINK git.exe git-mailinfo.exe)
file(CREATE_LINK git.exe git-mailsplit.exe)
file(CREATE_LINK git.exe git-merge-base.exe)
file(CREATE_LINK git.exe git-merge-file.exe)
file(CREATE_LINK git.exe git-merge-index.exe)
file(CREATE_LINK git.exe git-merge-ours.exe)
file(CREATE_LINK git.exe git-merge-recursive.exe)
file(CREATE_LINK git.exe git-merge-tree.exe)
file(CREATE_LINK git.exe git-merge.exe)
file(CREATE_LINK git.exe git-mktag.exe)
file(CREATE_LINK git.exe git-mktree.exe)
file(CREATE_LINK git.exe git-multi-pack-index.exe)
file(CREATE_LINK git.exe git-mv.exe)
file(CREATE_LINK git.exe git-name-rev.exe)
file(CREATE_LINK git.exe git-notes.exe)
file(CREATE_LINK git.exe git-pack-objects.exe)
file(CREATE_LINK git.exe git-pack-redundant.exe)
file(CREATE_LINK git.exe git-pack-refs.exe)
file(CREATE_LINK git.exe git-patch-id.exe)
file(CREATE_LINK git.exe git-prune-packed.exe)
file(CREATE_LINK git.exe git-prune.exe)
file(CREATE_LINK git.exe git-pull.exe)
file(CREATE_LINK git.exe git-push.exe)
file(CREATE_LINK git.exe git-range-diff.exe)
file(CREATE_LINK git.exe git-read-tree.exe)
file(CREATE_LINK git.exe git-rebase.exe)
file(CREATE_LINK git.exe git-receive-pack.exe)
file(CREATE_LINK git.exe git-reflog.exe)
file(CREATE_LINK git.exe git-refs.exe)
file(CREATE_LINK git.exe git-remote-ext.exe)
file(CREATE_LINK git.exe git-remote-fd.exe)
file(CREATE_LINK git.exe git-remote.exe)
file(CREATE_LINK git.exe git-repack.exe)
file(CREATE_LINK git.exe git-replace.exe)
file(CREATE_LINK git.exe git-replay.exe)
file(CREATE_LINK git.exe git-rerere.exe)
file(CREATE_LINK git.exe git-reset.exe)
file(CREATE_LINK git.exe git-rev-list.exe)
file(CREATE_LINK git.exe git-rev-parse.exe)
file(CREATE_LINK git.exe git-revert.exe)
file(CREATE_LINK git.exe git-rm.exe)
file(CREATE_LINK git.exe git-send-pack.exe)
file(CREATE_LINK git.exe git-shortlog.exe)
file(CREATE_LINK git.exe git-show-branch.exe)
file(CREATE_LINK git.exe git-show-index.exe)
file(CREATE_LINK git.exe git-show-ref.exe)
file(CREATE_LINK git.exe git-sparse-checkout.exe)
file(CREATE_LINK git.exe git-stash.exe)
file(CREATE_LINK git.exe git-stripspace.exe)
file(CREATE_LINK git.exe git-submodule--helper.exe)
file(CREATE_LINK git.exe git-symbolic-ref.exe)
file(CREATE_LINK git.exe git-tag.exe)
file(CREATE_LINK git.exe git-unpack-file.exe)
file(CREATE_LINK git.exe git-unpack-objects.exe)
file(CREATE_LINK git.exe git-update-index.exe)
file(CREATE_LINK git.exe git-update-ref.exe)
file(CREATE_LINK git.exe git-update-server-info.exe)
file(CREATE_LINK git.exe git-upload-archive.exe)
file(CREATE_LINK git.exe git-upload-pack.exe)
file(CREATE_LINK git.exe git-var.exe)
file(CREATE_LINK git.exe git-verify-commit.exe)
file(CREATE_LINK git.exe git-verify-pack.exe)
file(CREATE_LINK git.exe git-verify-tag.exe)
file(CREATE_LINK git.exe git-worktree.exe)
file(CREATE_LINK git.exe git-write-tree.exe)
file(CREATE_LINK git.exe git-cherry.exe)
file(CREATE_LINK git.exe git-cherry-pick.exe)
file(CREATE_LINK git.exe git-format-patch.exe)
file(CREATE_LINK git.exe git-fsck-objects.exe)
file(CREATE_LINK git.exe git-init.exe)
file(CREATE_LINK git.exe git-maintenance.exe)
file(CREATE_LINK git.exe git-merge-subtree.exe)
file(CREATE_LINK git.exe git-restore.exe)
file(CREATE_LINK git.exe git-show.exe)
file(CREATE_LINK git.exe git-stage.exe)
file(CREATE_LINK git.exe git-status.exe)
file(CREATE_LINK git.exe git-switch.exe)
file(CREATE_LINK git.exe git-version.exe)
file(CREATE_LINK git.exe git-whatchanged.exe)
file(CREATE_LINK git-remote-http.exe git-remote-https.exe)
file(CREATE_LINK git-remote-http.exe git-remote-ftp.exe)
file(CREATE_LINK git-remote-http.exe git-remote-ftps.exe)
