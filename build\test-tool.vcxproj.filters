﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-tool.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-advise.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-bitmap.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-bloom.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-bundle-uri.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-cache-tree.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-chmtime.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-config.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-crontab.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-csprng.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-date.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-delete-gpgsig.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-delta.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-dir-iterator.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-drop-caches.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-dump-cache-tree.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-dump-fsmonitor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-dump-split-index.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-dump-untracked-cache.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-env-helper.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-example-tap.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-find-pack.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-fsmonitor-client.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-genrandom.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-genzeros.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-getcwd.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-hash-speed.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-hash.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-hashmap.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-hexdump.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-json-writer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-lazy-init-name-hash.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-match-trees.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-mergesort.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-mktemp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-name-hash.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-online-cpus.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-pack-deltas.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-pack-mtimes.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-parse-options.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-parse-pathspec-file.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-partial-clone.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-path-utils.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-path-walk.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-pcre2-config.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-pkt-line.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-proc-receive.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-progress.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-reach.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-read-cache.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-read-graph.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-read-midx.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-ref-store.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-reftable.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-regex.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-rot13-filter.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-repository.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-revision-walking.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-run-command.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-scrap-cache-tree.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-serve-v2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-sha1.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-sha256.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-sigchain.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-simple-ipc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-string-list.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-submodule-config.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-submodule-nested-repo-config.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-submodule.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-subprocess.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-trace2.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-truncate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-userdiff.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-wildmatch.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-windows-named-pipe.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-write-cache.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-xml-encode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\helper\test-zlib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Object Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\test-lib.dir\$(Configuration)\test-lib.obj">
      <Filter>Object Libraries</Filter>
    </Object>
    <Object Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\common-main.dir\$(Configuration)\common-main.obj">
      <Filter>Object Libraries</Filter>
    </Object>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Object Libraries">
      <UniqueIdentifier>{DAE0ED53-0739-3571-A891-D5A852146035}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{C180A924-5CB5-3610-8243-A801B3263C3A}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
