# This is the CMakeCache file.
# For build in directory: c:/Users/<USER>/Desktop/workspace/tools/git/git/build
# It was generated by CMake: C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Build the testing tree.
BUILD_TESTING:BOOL=ON

//Path to a file.
CHARSET_INCLUDE_DIR:PATH=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/include

//Path to a library.
CHARSET_LIBRARY_DEBUG:FILEPATH=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/lib/charset.lib

//Path to a library.
CHARSET_LIBRARY_RELEASE:FILEPATH=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/lib/charset.lib

//Path to a program.
CMAKE_AR:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.exe

//Semicolon separated list of supported configuration types, only
// supports Debug, Release, MinSizeRel, and RelWithDebInfo, anything
// else will be ignored.
CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release;MinSizeRel;RelWithDebInfo

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=/DWIN32 /D_WINDOWS /W3

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=/MDd /Zi /Ob0 /Od /RTC1

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=/MD /O1 /Ob1 /DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=/MD /O2 /Ob2 /DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=/MD /Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/git

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Path to a program.
CMAKE_MT:FILEPATH=CMAKE_MT-NOTFOUND

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=git

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=2.50.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=2

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=50

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=rc

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=-DWIN32

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=-D_DEBUG

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//The CMake toolchain file
CMAKE_TOOLCHAIN_FILE:FILEPATH=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/scripts/buildsystems/vcpkg.cmake

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Path to the coverage program that CTest uses for performing coverage
// inspection
COVERAGE_COMMAND:FILEPATH=COVERAGE_COMMAND-NOTFOUND

//Extra command line flags to pass to the coverage tool
COVERAGE_EXTRA_FLAGS:STRING=-l

//How many times to retry timed-out CTest submissions.
CTEST_SUBMIT_RETRY_COUNT:STRING=3

//How long to wait between timed-out CTest submissions.
CTEST_SUBMIT_RETRY_DELAY:STRING=5

//The directory containing a CMake configuration file for CURL.
CURL_DIR:PATH=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/share/curl

//Maximum time allowed before CTest will kill the test.
DART_TESTING_TIMEOUT:STRING=1500

//Programs not built
EXCLUSION_PROGS_CACHE:STRING=empty

//Path to a file.
EXPAT_INCLUDE_DIR:PATH=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/include

EXPAT_LIBRARY:STRING=optimized;C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/lib/libexpat.lib;debug;C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/lib/libexpatd.lib

//Path to a library.
EXPAT_LIBRARY_DEBUG:FILEPATH=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/lib/libexpatd.lib

//Path to a library.
EXPAT_LIBRARY_RELEASE:FILEPATH=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/lib/libexpat.lib

//iconv include directory
Iconv_INCLUDE_DIR:PATH=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/include

//iconv library (if not in the C library)
Iconv_LIBRARY:FILEPATH=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/lib/iconv.lib

//Command to build the project
MAKECOMMAND:STRING=C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe --build . --config "${CTEST_CONFIGURATION_TYPE}"

//Path to the memory checking command, used for memory error detection.
MEMORYCHECK_COMMAND:FILEPATH=MEMORYCHECK_COMMAND-NOTFOUND

//File that contains suppressions for the memory checker
MEMORYCHECK_SUPPRESSIONS_FILE:FILEPATH=

//Path to a program.
MSGFMT_EXE:FILEPATH=MSGFMT_EXE-NOTFOUND

//Perform tests that use perl
PERL_TESTS:BOOL=ON

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=PKG_CONFIG_EXECUTABLE-NOTFOUND

//Perform tests that use python
PYTHON_TESTS:BOOL=ON

//Path to a program.
SH_EXE:FILEPATH=C:/Program Files/Git/usr/bin/sh.exe

//Name of the computer/site where compile is being run
SITE:STRING=13900k

//Skip hardlinking the dashed versions of the built-ins
SKIP_DASHED_BUILT_INS:BOOL=OFF

//Whether or not to use vcpkg for obtaining dependencies.  Only
// applicable to Windows platforms
USE_VCPKG:BOOL=ON

//Automatically copy dependencies into the output directory for
// executables.
VCPKG_APPLOCAL_DEPS:BOOL=ON

//The directory which contains the installed libraries for each
// triplet
VCPKG_INSTALLED_DIR:PATH=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed

//The path to the vcpkg manifest directory.
VCPKG_MANIFEST_DIR:PATH=

//Use manifest mode, as opposed to classic mode.
VCPKG_MANIFEST_MODE:BOOL=OFF

//Appends the vcpkg paths to CMAKE_PREFIX_PATH, CMAKE_LIBRARY_PATH
// and CMAKE_FIND_ROOT_PATH so that vcpkg libraries/packages are
// found after toolchain/system libraries/packages.
VCPKG_PREFER_SYSTEM_LIBS:BOOL=OFF

//Enable the setup of CMAKE_PROGRAM_PATH to vcpkg paths
VCPKG_SETUP_CMAKE_PROGRAM_PATH:BOOL=ON

//Vcpkg target triplet (ex. x86-windows)
VCPKG_TARGET_TRIPLET:STRING=x64-windows

//Trace calls to find_package()
VCPKG_TRACE_FIND_PACKAGE:BOOL=OFF

//Enables messages from the VCPKG toolchain for debugging purposes.
VCPKG_VERBOSE:BOOL=OFF

//(experimental) Add USES_TERMINAL to VCPKG_APPLOCAL_DEPS to force
// serialization.
X_VCPKG_APPLOCAL_DEPS_SERIALIZED:BOOL=OFF

//Path to a file.
ZLIB_INCLUDE_DIR:PATH=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/include

//Path to a library.
ZLIB_LIBRARY_DEBUG:FILEPATH=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/lib/zlibd.lib

//Path to a library.
ZLIB_LIBRARY_RELEASE:FILEPATH=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/lib/zlib.lib

//Path to a program.
Z_VCPKG_BUILTIN_POWERSHELL_PATH:FILEPATH=C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe

//Path to a program.
Z_VCPKG_PWSH_PATH:FILEPATH=Z_VCPKG_PWSH_PATH-NOTFOUND

//The directory which contains the installed libraries for each
// triplet
_VCPKG_INSTALLED_DIR:PATH=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed

//Value Computed by CMake
git_BINARY_DIR:STATIC=C:/Users/<USER>/Desktop/workspace/tools/git/git/build

//Value Computed by CMake
git_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
git_SOURCE_DIR:STATIC=C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CHARSET_LIBRARY_DEBUG
CHARSET_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CHARSET_LIBRARY_RELEASE
CHARSET_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=c:/Users/<USER>/Desktop/workspace/tools/git/git/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=30
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/bin/cpack.exe
//ADVANCED property for variable: CMAKE_CTEST_COMMAND
CMAKE_CTEST_COMMAND-ADVANCED:INTERNAL=1
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/bin/ctest.exe
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Visual Studio 17 2022
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Enterprise
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MT
CMAKE_MT-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TOOLCHAIN_FILE
CMAKE_TOOLCHAIN_FILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: COVERAGE_COMMAND
COVERAGE_COMMAND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: COVERAGE_EXTRA_FLAGS
COVERAGE_EXTRA_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CTEST_SUBMIT_RETRY_COUNT
CTEST_SUBMIT_RETRY_COUNT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CTEST_SUBMIT_RETRY_DELAY
CTEST_SUBMIT_RETRY_DELAY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_LIBRARY_DEBUG
CURL_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//vcpkg
CURL_LIBRARY_DEBUG:INTERNAL=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/lib/libcurl-d.lib
//ADVANCED property for variable: CURL_LIBRARY_RELEASE
CURL_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//vcpkg
CURL_LIBRARY_RELEASE:INTERNAL=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/lib/libcurl.lib
//ADVANCED property for variable: DART_TESTING_TIMEOUT
DART_TESTING_TIMEOUT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: EXPAT_INCLUDE_DIR
EXPAT_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: EXPAT_LIBRARY
EXPAT_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: EXPAT_LIBRARY_DEBUG
EXPAT_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: EXPAT_LIBRARY_RELEASE
EXPAT_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//Details about finding EXPAT
FIND_PACKAGE_MESSAGE_DETAILS_EXPAT:INTERNAL=[optimized;C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/lib/libexpat.lib;debug;C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/lib/libexpatd.lib][C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/include][v2.7.1()]
//Details about finding Iconv
FIND_PACKAGE_MESSAGE_DETAILS_Iconv:INTERNAL=[C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/lib/iconv.lib][C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/include][v1.18()]
//Details about finding ZLIB
FIND_PACKAGE_MESSAGE_DETAILS_ZLIB:INTERNAL=[optimized;C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/lib/zlib.lib;debug;C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/debug/lib/zlibd.lib][C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg/installed/x64-windows/include][c ][v1.3.1(1)]
//Test FREAD_READS_DIRECTORIES_NO
FREAD_READS_DIRECTORIES_NO:INTERNAL=1
//Result of TRY_COMPILE
FREAD_READS_DIRECTORIES_NO_COMPILED:INTERNAL=TRUE
//Result of try_run()
FREAD_READS_DIRECTORIES_NO_EXITCODE:INTERNAL=0
//Test HAVE_ALLOCA_H
HAVE_ALLOCA_H:INTERNAL=
//Test HAVE_BSD_SYSCTL
HAVE_BSD_SYSCTL:INTERNAL=
//Have function clock_gettime
HAVE_CLOCK_GETTIME:INTERNAL=
//Have symbol CLOCK_MONOTONIC
HAVE_CLOCK_MONOTONIC:INTERNAL=
//Have function getdelim
HAVE_GETDELIM:INTERNAL=
//Have include inttypes.h
HAVE_INTTYPES_H:INTERNAL=1
//Have include libgen.h
HAVE_LIBGEN_H:INTERNAL=
//Have function memmem
HAVE_MEMMEM:INTERNAL=
//Have function mkdtemp
HAVE_MKDTEMP:INTERNAL=
//Test HAVE_NEW_ICONV
HAVE_NEW_ICONV:INTERNAL=1
//Have include paths.h
HAVE_PATHS_H:INTERNAL=
//Have function poll
HAVE_POLL:INTERNAL=
//Have include poll.h
HAVE_POLL_H:INTERNAL=
//Have function pread
HAVE_PREAD:INTERNAL=
//Test HAVE_REGEX
HAVE_REGEX:INTERNAL=
//Have function setenv
HAVE_SETENV:INTERNAL=
//Have function strcasestr
HAVE_STRCASESTR:INTERNAL=
//Have include strings.h
HAVE_STRINGS_H:INTERNAL=
//Have function strlcpy
HAVE_STRLCPY:INTERNAL=
//Have function strtoimax
HAVE_STRTOIMAX:INTERNAL=1
//Have function strtoull
HAVE_STRTOULL:INTERNAL=1
//Have function strtoumax
HAVE_STRTOUMAX:INTERNAL=1
//Have include sys/sysinfo.h
HAVE_SYSINFO:INTERNAL=
//Have include sys/poll.h
HAVE_SYS_POLL_H:INTERNAL=
//Have include sys/select.h
HAVE_SYS_SELECT_H:INTERNAL=
//Test ICONV_DOESNOT_OMIT_BOM
ICONV_DOESNOT_OMIT_BOM:INTERNAL=
//Result of TRY_COMPILE
ICONV_DOESNOT_OMIT_BOM_COMPILED:INTERNAL=TRUE
//Result of try_run()
ICONV_DOESNOT_OMIT_BOM_EXITCODE:INTERNAL=FAILED_TO_RUN
//ADVANCED property for variable: Iconv_INCLUDE_DIR
Iconv_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Iconv_LIBRARY
Iconv_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MAKECOMMAND
MAKECOMMAND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MEMORYCHECK_COMMAND
MEMORYCHECK_COMMAND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MEMORYCHECK_SUPPRESSIONS_FILE
MEMORYCHECK_SUPPRESSIONS_FILE-ADVANCED:INTERNAL=1
PC_EXPAT_CFLAGS:INTERNAL=
PC_EXPAT_CFLAGS_I:INTERNAL=
PC_EXPAT_CFLAGS_OTHER:INTERNAL=
PC_EXPAT_FOUND:INTERNAL=
PC_EXPAT_INCLUDEDIR:INTERNAL=
PC_EXPAT_LIBDIR:INTERNAL=
PC_EXPAT_LIBS:INTERNAL=
PC_EXPAT_LIBS_L:INTERNAL=
PC_EXPAT_LIBS_OTHER:INTERNAL=
PC_EXPAT_LIBS_PATHS:INTERNAL=
PC_EXPAT_MODULE_NAME:INTERNAL=
PC_EXPAT_PREFIX:INTERNAL=
PC_EXPAT_STATIC_CFLAGS:INTERNAL=
PC_EXPAT_STATIC_CFLAGS_I:INTERNAL=
PC_EXPAT_STATIC_CFLAGS_OTHER:INTERNAL=
PC_EXPAT_STATIC_LIBDIR:INTERNAL=
PC_EXPAT_STATIC_LIBS:INTERNAL=
PC_EXPAT_STATIC_LIBS_L:INTERNAL=
PC_EXPAT_STATIC_LIBS_OTHER:INTERNAL=
PC_EXPAT_STATIC_LIBS_PATHS:INTERNAL=
PC_EXPAT_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SITE
SITE-ADVANCED:INTERNAL=1
//Test SNPRINTF_OK
SNPRINTF_OK:INTERNAL=1
//Result of TRY_COMPILE
SNPRINTF_OK_COMPILED:INTERNAL=TRUE
//Result of try_run()
SNPRINTF_OK_EXITCODE:INTERNAL=0
//Test STRUCT_STAT_HAS_ST_BLOCKS
STRUCT_STAT_HAS_ST_BLOCKS:INTERNAL=
//Install the dependencies listed in your manifest:
//\n    If this is off, you will have to manually install your dependencies.
//\n    See https://github.com/microsoft/vcpkg/tree/master/docs/specifications/manifests.md
// for more info.
//\n
VCPKG_MANIFEST_INSTALL:INTERNAL=OFF
//ADVANCED property for variable: VCPKG_VERBOSE
VCPKG_VERBOSE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_INCLUDE_DIR
ZLIB_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_DEBUG
ZLIB_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_RELEASE
ZLIB_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//Making sure VCPKG_MANIFEST_MODE doesn't change
Z_VCPKG_CHECK_MANIFEST_MODE:INTERNAL=OFF
//The path to the PowerShell implementation to use.
Z_VCPKG_POWERSHELL_PATH:INTERNAL=C:/Windows/System32/WindowsPowerShell/v1.0/powershell.exe
//Vcpkg root directory
Z_VCPKG_ROOT_DIR:INTERNAL=C:/Users/<USER>/Desktop/workspace/tools/git/git/compat/vcbuild/vcpkg
__pkg_config_checked_PC_EXPAT:INTERNAL=1

