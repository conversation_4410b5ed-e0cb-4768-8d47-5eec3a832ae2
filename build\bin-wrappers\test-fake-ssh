#!/bin/sh

# wrap-for-bin.sh: Template for git executable wrapper scripts
# to run test suite against sandbox, but with only bindir-installed
# executables in PATH.  The Makefile copies this into various
# files in bin-wrappers, substituting
# C:/Users/<USER>/Desktop/workspace/tools/git/git/build, 'C:/Users/<USER>/Desktop/workspace/tools/git/git/build/templates/blt' and C:/Users/<USER>/Desktop/workspace/tools/git/git/build/t/helper/test-fake-ssh.exe.

GIT_EXEC_PATH='C:/Users/<USER>/Desktop/workspace/tools/git/git/build'
if test -n "$NO_SET_GIT_TEMPLATE_DIR"
then
	unset GIT_TEMPLATE_DIR
else
	GIT_TEMPLATE_DIR=''C:/Users/<USER>/Desktop/workspace/tools/git/git/build/templates/blt''
	export GIT_TEMPLATE_DIR
fi
MERGE_TOOLS_DIR='@MERGE_TOOLS_DIR@'
GITPERLLIB='@GITPERLLIB@'"${GITPERLLIB:+:$GITPERLLIB}"
GIT_TEXTDOMAINDIR='@GIT_TEXTDOMAINDIR@'
PATH='C:/Users/<USER>/Desktop/workspace/tools/git/git/build/bin-wrappers:'"$PATH"

export MERGE_TOOLS_DIR GIT_EXEC_PATH GITPERLLIB PATH GIT_TEXTDOMAINDIR

case "$GIT_DEBUGGER" in
'')
	exec "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/t/helper/test-fake-ssh.exe" "$@"
	;;
1)
	unset GIT_DEBUGGER
	exec gdb --args "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/t/helper/test-fake-ssh.exe" "$@"
	;;
*)
	GIT_DEBUGGER_ARGS="$GIT_DEBUGGER"
	unset GIT_DEBUGGER
	exec ${GIT_DEBUGGER_ARGS} "C:/Users/<USER>/Desktop/workspace/tools/git/git/build/t/helper/test-fake-ssh.exe" "$@"
	;;
esac
