BROKEN_PATH_FIX=
DIFF='diff'
FSMONITOR_DAEMON_BACKEND=win32
FSMONITOR_OS_SETTINGS=win32
GITWEBDIR='/mingw64/share/locale'
GIT_INTEROP_MAKE_OPTS=
GIT_PERF_LARGE_REPO=
GIT_PERF_MAKE_COMMAND=
GIT_PERF_MAKE_OPTS=
GIT_PERF_REPEAT_COUNT=
GIT_PERF_REPO=
GIT_SOURCE_DIR=C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems/../..
GIT_TEST_CMP=
GIT_TEST_CMP_USE_COPIED_CONTEXT=
GIT_TEST_GITPERLLIB='C:/Users/<USER>/Desktop/workspace/tools/git/git/build/perl/build/lib'
GIT_TEST_INDEX_VERSION=
GIT_TEST_OPTS=
GIT_TEST_PERL_FATAL_WARNINGS=
GIT_TEST_TEMPLATE_DIR='C:/Users/<USER>/Desktop/workspace/tools/git/git/build/templates/blt'
GIT_TEST_TEXTDOMAINDIR='C:/Users/<USER>/Desktop/workspace/tools/git/git/build/po/build/locale'
GIT_TEST_UTF8_LOCALE=
LOCALEDIR='/mingw64/share/locale'
NO_CURL=
NO_EXPAT=
NO_GETTEXT=1
NO_GITWEB=1
NO_ICONV=
NO_PERL=
NO_PERL_CPAN_FALLBACKS=
NO_PTHREADS=
NO_PYTHON=
NO_REGEX=
NO_UNIX_SOCKETS=1
PAGER_ENV='LESS=FRX LV=-c'
PERL_LOCALEDIR='/mingw64/share/locale'
PERL_PATH='/usr/bin/perl'
PYTHON_PATH='/usr/bin/python'
RUNTIME_PREFIX='true'
SANITIZE_ADDRESS=
SANITIZE_LEAK=
SHELL_PATH='/bin/sh'
TAR='tar'
TEST_OUTPUT_DIRECTORY=
TEST_SHELL_PATH='/bin/sh'
USE_GETTEXT_SCHEME=
USE_LIBPCRE2=
WITH_BREAKING_CHANGES=
X=.exe
PATH="$PATH:$TEST_DIRECTORY/../compat/vcbuild/vcpkg/installed/x64-windows/bin"
