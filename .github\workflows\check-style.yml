name: check-style

# Get the repository with all commits to ensure that we can analyze
# all of the commits contributed via the Pull Request.

on:
  pull_request:
    types: [opened, synchronize]

# Avoid unnecessary builds. Unlike the main CI jobs, these are not
# ci-configurable (but could be).
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  check-style:
    env:
      CC: clang
      jobname: ClangFormat
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - run: ci/install-dependencies.sh

    - name: git clang-format
      continue-on-error: true
      id: check_out
      run: |
        ./ci/run-style-check.sh \
          "${{github.event.pull_request.base.sha}}"
