﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{BE5BBB74-B7FF-3295-8E80-66C008EBFEAD}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Experimental</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\vcbuild\include;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\win32;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\poll;C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\..\..\compat\regex;C:\Users\<USER>\Desktop\workspace\tools\git\git\build;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\15e124c2f35056f23304497decf09d62\Experimental.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\ctest.exe -C Debug -D Experimental
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\Experimental</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\ctest.exe -C Release -D Experimental
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\Experimental</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\ctest.exe -C MinSizeRel -D Experimental
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\Experimental</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\ctest.exe -C RelWithDebInfo -D Experimental
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\Experimental</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -SC:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems -BC:/Users/<USER>/Desktop/workspace/tools/git/git/build --check-stamp-file C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTest.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestUseLaunchers.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckFunctionExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckStructHasMember.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckTypeSize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\DartConfiguration.tcl.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindEXPAT.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindIconv.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPkgConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\expat\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\iconv\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\scripts\buildsystems\vcpkg.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\description;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\applypatch-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\fsmonitor-watchman.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\post-update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-applypatch.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-merge-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-push.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-rebase.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-receive.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\prepare-commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\push-to-checkout.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\sendemail-validate.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\info\exclude;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -SC:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems -BC:/Users/<USER>/Desktop/workspace/tools/git/git/build --check-stamp-file C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTest.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestUseLaunchers.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckFunctionExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckStructHasMember.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckTypeSize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\DartConfiguration.tcl.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindEXPAT.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindIconv.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPkgConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\expat\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\iconv\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\scripts\buildsystems\vcpkg.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\description;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\applypatch-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\fsmonitor-watchman.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\post-update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-applypatch.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-merge-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-push.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-rebase.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-receive.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\prepare-commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\push-to-checkout.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\sendemail-validate.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\info\exclude;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -SC:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems -BC:/Users/<USER>/Desktop/workspace/tools/git/git/build --check-stamp-file C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTest.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestUseLaunchers.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckFunctionExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckStructHasMember.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckTypeSize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\DartConfiguration.tcl.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindEXPAT.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindIconv.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPkgConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\expat\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\iconv\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\scripts\buildsystems\vcpkg.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\description;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\applypatch-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\fsmonitor-watchman.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\post-update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-applypatch.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-merge-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-push.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-rebase.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-receive.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\prepare-commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\push-to-checkout.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\sendemail-validate.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\info\exclude;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin\cmake.exe -SC:/Users/<USER>/Desktop/workspace/tools/git/git/contrib/buildsystems -BC:/Users/<USER>/Desktop/workspace/tools/git/git/build --check-stamp-file C:/Users/<USER>/Desktop/workspace/tools/git/git/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\3.30.1\CMakeSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakePushCheckState.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTest.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CTestUseLaunchers.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckFunctionExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckStructHasMember.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckSymbolExists.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckTypeSize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\DartConfiguration.tcl.in;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindEXPAT.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindIconv.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPkgConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CheckSourceRuns.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\expat\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\iconv\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\compat\vcbuild\vcpkg\scripts\buildsystems\vcpkg.cmake;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\description;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\applypatch-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\fsmonitor-watchman.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\post-update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-applypatch.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-merge-commit.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-push.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-rebase.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\pre-receive.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\prepare-commit-msg.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\push-to-checkout.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\sendemail-validate.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\hooks\update.sample;C:\Users\<USER>\Desktop\workspace\tools\git\git\templates\info\exclude;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\Experimental">
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\ZERO_CHECK.vcxproj">
      <Project>{1EA13B83-14DD-3A1E-96AC-46FB2F067586}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>