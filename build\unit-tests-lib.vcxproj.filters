﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\u-ctype.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\u-example-decorate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\u-hash.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\u-hashmap.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\u-mem-pool.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\u-oid-array.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\u-oidmap.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\u-oidtree.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\u-prio-queue.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\u-reftable-tree.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\u-strbuf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\u-strcmp-offset.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\u-strvec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\u-trailer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\t\unit-tests\u-urlmatch-normalization.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests\clar-decls.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\dcfc9d17498fb2219ae9038910c4eb81\clar-decls.h.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\CMakeFiles\dcfc9d17498fb2219ae9038910c4eb81\clar.suite.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\contrib\buildsystems\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="C:\Users\<USER>\Desktop\workspace\tools\git\git\build\t\unit-tests\clar.suite" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{FD4F856E-57BF-3C53-A628-8AAFDE1B7E8A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{D9E7C97D-3AD5-3E6D-A2C9-BE7A6651FBC4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{C180A924-5CB5-3610-8243-A801B3263C3A}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
