diff_cmd () {
	"$merge_tool_path" "$LOCAL" "$REMOTE"
}

diff_cmd_help () {
	echo "Use Code Compare (requires a graphical session)"
}

merge_cmd () {
	if $base_present
	then
		"$merge_tool_path" -MF="$LOCAL" -TF="$REMOTE" -BF="$BASE" \
			-RF="$MERGED"
	else
		"$merge_tool_path" -MF="$LOCAL" -TF="$REMOTE" \
			-RF="$MERGED"
	fi
}

merge_cmd_help () {
	echo "Use Code Compare (requires a graphical session)"
}

translate_merge_tool_path() {
	if merge_mode
	then
		echo CodeMerge
	else
		echo CodeCompare
	fi
}
